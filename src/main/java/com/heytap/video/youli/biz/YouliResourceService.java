package com.heytap.video.youli.biz;

import com.heytap.video.fallback.model.FallbackResponse;
import com.heytap.video.fallback.util.FallbackDataUtil;
import com.heytap.video.youli.biz.algorithm.LongVideoTrailerRecommendService;
import com.heytap.video.youli.biz.algorithm.RecommendService;
import com.heytap.video.youli.biz.contentmiddle.CommentService;
import com.heytap.video.youli.biz.contentmiddle.MiddleMediaResource;
import com.heytap.video.youli.biz.contentmiddle.MiddlePondResource;
import com.heytap.video.youli.biz.contentmiddle.MiddleVideoResource;
import com.heytap.video.youli.cache.AlgorithmPoolCache;
import com.heytap.video.youli.config.LongVideoConfig;
import com.heytap.video.youli.config.LongVideoTrailerRecommendConfig;
import com.heytap.video.youli.config.ResourceConfig;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.constant.Bidlst;
import com.heytap.video.youli.metric.MetricConstant;
import com.heytap.video.youli.metric.YouliMetrics;
import com.heytap.video.youli.model.ExtraInfo;
import com.heytap.video.youli.model.QualityComparable;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmResponse;
import com.heytap.video.youli.model.algorithm.Recommend;
import com.heytap.video.youli.model.algorithm.RecommendList;
import com.heytap.video.youli.model.content.Author;
import com.heytap.video.youli.model.content.FilterWord;
import com.heytap.video.youli.model.content.VideoCountInfo;
import com.heytap.video.youli.model.content.VideoDetailInfo;
import com.heytap.video.youli.model.content.VideoInfo;
import com.heytap.video.youli.model.content.VideoResource;
import com.heytap.video.youli.utils.BizUtils;
import com.oppo.browser.app.framework.utils.MyHttpUtils;
import com.oppo.browser.app.framework.utils.MyListUtils;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.protobuf.StringBuilderHolder;
import com.oppo.browser.common.pubobj.feeds.exceptions.BadRequestException;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.video.common.pubobj.FeedsConstant;
import com.oppo.browser.video.common.pubobj.constant.RelationTypeConstant;
import com.oppo.browser.video.common.pubobj.constant.StyleTypeConstant;
import com.oppo.browser.video.common.pubobj.resource.BatchArticleInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.GetPondVideosReqProperty;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoMediaInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoMediaListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoRecListReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.ad.AppInfo;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Article;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Item;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Medium;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.ReasonObj;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.RelationInfo;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Video;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.VideoMediaInfo;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.VideoUrl;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import com.oppo.cpc.video.framework.lib.appstore.AppDetailInfo;
import com.oppo.cpc.video.framework.lib.appstore.AppStoreService;
import com.oppo.cpc.video.framework.lib.exception.BadRequestRuntimeException;
import com.oppo.cpc.video.framework.lib.metrics.MonitorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.heytap.video.youli.cache.AlgorithmPoolCache.FALLBACK_TYPE_CONVERT_ERROR;

@Slf4j
@Service
public class YouliResourceService {
    @Autowired
    private RecommendService recommendService;

    @Autowired
    private LongVideoTrailerRecommendService lvtRecommendService;

    @Autowired
    private YouliMetrics youliMetrics;

    @Autowired
    private MiddleVideoResource middleVideoResource;

    @Autowired
    private MiddleMediaResource middleMediaResource;

    @Autowired
    private CommentService commentService;

    @Autowired
    private MiddlePondResource middlePondResource;

    @Autowired
    private AppStoreService appStoreService;

    @Autowired
    private YouliApiConfig youliApiConfig;

    @Autowired
    private LongVideoTrailerRecommendConfig longVideoTrailerRecommendConfig;

    @Autowired
    private LongVideoResource longVideoResource;

    @Autowired
    private LongVideoConfig longVideoConfig;

    @Autowired
    private AlgorithmPoolCache algorithmPoolCache;


//    private static final int EDU_LONG_VIDEO_SUPPORT_MIN_VERSION = 48;
//    private static final String EDU_LONG_VIDEO_RELATED_TYPE = "eduLongVideo";

    /**
     * 获取各频道视频列表
     *
     * <p>该方法是重构后的主方法，通过调用多个子方法来处理不同类型的视频列表请求。
     * 重构前该方法的圈复杂度为45，重构后降低到8以下。</p>
     *
     * <h3>重构策略：</h3>
     * <ul>
     *   <li>方法分解（Extract Method）- 将大方法分解为多个小方法</li>
     *   <li>策略模式 - 按频道类型将处理逻辑分为不同的策略方法</li>
     *   <li>条件提取 - 将复杂条件判断提取为独立方法</li>
     *   <li>通用方法提取 - 提取重复的算法处理逻辑</li>
     * </ul>
     *
     * <h3>处理流程：</h3>
     * <ol>
     *   <li>中台内容池频道处理</li>
     *   <li>长视频预告推荐处理</li>
     *   <li>UGC视频推荐处理</li>
     *   <li>风行短视频推荐处理</li>
     *   <li>默认推荐处理</li>
     * </ol>
     *
     * @param reqProperty 请求属性对象，包含所有请求相关信息
     * @return 视频列表响应的CompletableFuture
     * @throws BizException 业务异常
     */
    public CompletableFuture<VideoFeedsListRt> getFeedsList(FeedsListReqProperty reqProperty) {
        // 处理中台内容池频道
        if (isPondChannelRequest(reqProperty)) {
            return handlePondChannelRequest(reqProperty);
        }

        // 处理长视频预告推荐
        if (isTrailerRecommendRequest(reqProperty)) {
            return handleTrailerRecommendRequest(reqProperty);
        }

        // 处理UGC视频推荐
        if (isUgcRequest(reqProperty)) {
            return handleUgcRequest(reqProperty);
        }

        // 处理风行短视频推荐
        if (isFengxingShortRequest(reqProperty)) {
            return handleFengxingShortRequest(reqProperty);
        }

        // 默认推荐处理
        return handleDefaultRecommendRequest(reqProperty);
    }

    /**
     * 处理内容池视频数据
     *
     * <p>将内容池视频资源转换为标准的视频列表响应，包括评论数量的异步获取和设置。</p>
     *
     * <h3>处理流程：</h3>
     * <ol>
     *   <li>提取视频ID列表</li>
     *   <li>转换为标准的FeedsList格式</li>
     *   <li>异步获取评论数量</li>
     *   <li>设置评论数量到文章列表</li>
     *   <li>为频道请求设置最后一个视频的热门时间</li>
     *   <li>构建并返回响应对象</li>
     * </ol>
     *
     * @param reqProperty 请求属性对象
     * @param pondVideos 内容池视频资源列表
     * @param isChannel 是否为频道请求，影响热门时间的设置
     * @return 视频列表响应的CompletableFuture
     */
    private CompletableFuture<VideoFeedsListRt> handlePondVideos(FeedsListReqProperty reqProperty, List<VideoResource> pondVideos, boolean isChannel) {
        List<String> videoIds = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(pondVideos)) {
            for (VideoResource resource : pondVideos) {
                videoIds.add(resource.getId());
            }
        }
        FeedsList feedsList = getFromContentMiddle(reqProperty, pondVideos, null);
        return commentService.getArticlesCmtCountAsync(videoIds).handle((cmtCount, throwable) -> {
            commentService.setArticlesCmtCount(feedsList.getArticles(), cmtCount);
            if (CollectionUtils.isNotEmpty(feedsList.getArticles()) && isChannel) {
                feedsList.getArticles().get(feedsList.getArticles().size() - 1).setBeHotTime(reqProperty.getBottomtime().longValue());
            }
            VideoFeedsListRt rt = new VideoFeedsListRt();
            rt.setRet(0);
            rt.setMsg("success");
            rt.setData(feedsList);
            return rt;
        });
    }

    /**
     * 处理推荐兜底响应
     *
     * <p>处理推荐服务返回的兜底响应，包括兜底数据和正常推荐数据的处理。</p>
     *
     * <h3>处理逻辑：</h3>
     * <ul>
     *   <li>响应为空或无效：返回错误响应（1400）</li>
     *   <li>兜底数据：直接使用兜底数据并上报指标</li>
     *   <li>正常推荐数据：验证数据有效性，转换格式，写入兜底缓存</li>
     * </ul>
     *
     * <h3>错误处理：</h3>
     * <ul>
     *   <li>推荐列表为空或返回码非0：返回1400错误</li>
     *   <li>推荐结果为空：返回1400错误</li>
     * </ul>
     *
     * @param reqProperty 请求属性对象
     * @param response 推荐服务的兜底响应
     * @return 视频列表响应对象
     */
    private VideoFeedsListRt handleRecommendFallbackResponse(FeedsListReqProperty reqProperty, FallbackResponse<RecommendList, FeedsList> response) {
        VideoFeedsListRt rt = new VideoFeedsListRt();
        if (response == null || (!response.isFallbackData() && response.getValue() == null) || (response.isFallbackData() && response.getFallbackValue() == null)) {
            log.warn("empty FeedsRecommendResponse, req:{}, response:{}", "", response);
            rt.setRet(1400);
            rt.setMsg("empty FeedsRecommendResponse[{}][{}][{}]");
            return rt;
        }
        if (response.isFallbackData()) {
            youliMetrics.recommendFallbackReport(response.getFallbackValue(), reqProperty);
            rt.setData(response.getFallbackValue());
            rt.setRet(0);
            return rt;
        }
        RecommendList recommendList = response.getValue();
        youliMetrics.recommendApiRetReport(recommendList, reqProperty);
        if ((recommendList == null) || (recommendList.getCode() != 0) || MyListUtils.isEmpty(recommendList.getResult())) {
            log.warn("recommend_api :[url:{}][rt:{}]", "", response.getValue());
            rt.setRet(1400);
            rt.setMsg("recommend result:" + (recommendList != null ? (recommendList.getMessage() + "[code:" + recommendList.getCode() + "]") : null));
            return rt;
        }
        ExtraInfo extraInfo = buildRecommendExtraInfo(recommendList, Bidlst.CHANNEL);
        rt.setRet(0);
        rt.setMsg("success");
        rt.setData(getFromContentMiddle(reqProperty, recommendList.getResult(), extraInfo));
        writeFallbackData(reqProperty, rt.getData());
        return rt;
    }


    /**
     * 构建推荐额外信息
     *
     * <p>根据推荐列表和推荐栏ID构建额外信息对象，用于后续的数据处理和统计。</p>
     *
     * @param recommendList 推荐列表对象，包含推荐ID等信息
     * @param bidlst 推荐栏ID，标识推荐位置
     * @return 构建完成的额外信息对象
     */
    private ExtraInfo buildRecommendExtraInfo(RecommendList recommendList, String bidlst) {
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setRid(recommendList.getRid());
        extraInfo.setBidlst(bidlst);
        return extraInfo;
    }

    /**
     * 安全获取Future结果
     *
     * <p>从Future对象中获取结果，忽略异常并返回null。
     * 这是一个通用的工具方法，用于处理可能失败的异步操作。</p>
     *
     * <h3>异常处理：</h3>
     * <ul>
     *   <li>Future为null：返回null</li>
     *   <li>获取结果时发生异常：记录警告日志并返回null</li>
     *   <li>正常情况：返回Future的结果</li>
     * </ul>
     *
     * @param <T> Future结果的类型
     * @param future 要获取结果的Future对象
     * @return Future的结果，如果获取失败则返回null
     */
    private <T> T getFutureIgnoreException(Future<T> future) {
        try {
            return (future != null) ? future.get() : null;
        } catch (Exception e) {
            log.warn("error get result from future", e);
            return null;
        }
    }

    /**
     * 获取相关推荐视频列表
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<FeedsList> getRecVideoV1(VideoRecListReqProperty reqProperty) {
        String id = reqProperty.getDocid();
        if (StringUtils.isEmpty(id)) {
            log.warn("invalid docid:{}", reqProperty.getDocid());
            CompletableFuture future = new CompletableFuture();
            future.completeExceptionally(new BadRequestRuntimeException("invalid docId:" + reqProperty.getDocid()));
            return future;
        }
        // 兜底算法请求参数处理
        if (reqProperty.getOffset() == 0) {
            reqProperty.setDownTimes(0);
        } else {
            reqProperty.setUpTimes(reqProperty.getRefreshTimes());
        }
        // 8.3 短小切自建 ，相关推荐统一修改为经过算法
        // 暂时注释掉从媒体号取视频作为相关推荐视频列表内容的逻辑，  后续需要可取消注释恢复
//        if ("subv_game".equals(reqProperty.getFromId())) {
            return recommendService.getRelatedRecommend(reqProperty).thenApply(recommendList -> {
                ExtraInfo extraInfo = buildRecommendExtraInfo(recommendList, BizUtils.getRelevantBidLst(youliApiConfig));
                return getFromContentMiddle(reqProperty, recommendList.getResult(), extraInfo);
            });
//        }
//        CompletableFuture<String> mediaNoCf;
//        if (StringUtils.isEmpty(reqProperty.getMediaNo())) {
//            List<String> docIds = new ArrayList<>();
//            docIds.add(id);
//            mediaNoCf = middleVideoResource.getResourceListCf(reqProperty, docIds).thenApply(resourceList -> {
//                if (CollectionUtils.isEmpty(resourceList) || resourceList.get(0) == null) {
//                    log.warn("{} is not exist article", id);
//                    throw new BadRequestRuntimeException("this article is not exist");
//                }
//                return resourceList.get(0).getAuthor().getId();
//            });
//        } else {
//            mediaNoCf = CompletableFuture.completedFuture(reqProperty.getMediaNo());
//        }
//        return mediaNoCf.thenCompose(mediaNo -> {
//            VideoMediaListReqProperty mediaReqProperty = new VideoMediaListReqProperty();
//            mediaReqProperty.setMediaNo(mediaNo);
//            mediaReqProperty.setType("0");
//            if (reqProperty.getDetailStyle() == 3) {
//                // detailStyle=3 2N详情页 仅获取4个相关推荐
//                mediaReqProperty.setNumber(youliApiConfig.getVideoRec2NLimit());
//            } else {
//                mediaReqProperty.setNumber(15);
//            }
//            mediaReqProperty.setAttributeValues(reqProperty.getAttributeValues());
//            mediaReqProperty.setOffset(0);
//            return getMediaList(mediaReqProperty).thenApply(feedsList -> {
//                if (feedsList == null || CollectionUtils.isEmpty(feedsList.getItems())) {
//                    return feedsList;
//                }
//                Iterator<Item> iterator = feedsList.getItems().iterator();
//                while (iterator.hasNext()) {
//                    if (id.equals(iterator.next().getId())) {
//                        iterator.remove();
//                        break;
//                    }
//                }
//                Collections.shuffle(feedsList.getItems());
//                return feedsList;
//            });
//        });
    }

    /**
     * 获取单个视频详情
     *
     * @param articleInfoReqProperty
     * @return
     * @throws BizException
     */
    public Article getArticleInfo(VideoInfoReqProperty articleInfoReqProperty) throws BizException {
        String id = articleInfoReqProperty.getDocid();
        if (StringUtils.isEmpty(id)) {
            log.warn("invalid docid:{}", articleInfoReqProperty.getDocid());
            throw new BadRequestException("invalid docId:" + articleInfoReqProperty.getDocid());
        }

        List<String> docIds = new ArrayList<>();
        docIds.add(articleInfoReqProperty.getDocid());
        FeedsListReqProperty feedsListReqProperty = new FeedsListReqProperty();
        feedsListReqProperty.setSource(articleInfoReqProperty.getSource());
        feedsListReqProperty.setFeedssession(articleInfoReqProperty.getFeedssession());
        feedsListReqProperty.setVersion(articleInfoReqProperty.getVersion());
        feedsListReqProperty.setFromId(articleInfoReqProperty.getFromId());
        feedsListReqProperty.setAttributeValues(articleInfoReqProperty.getAttributeValues());
        List<VideoResource> resourceList = middleVideoResource.getResourceList(feedsListReqProperty, docIds);
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setDpOpenFrom(articleInfoReqProperty.getDpOpenFrom());
        FeedsList feedsList = getFromContentMiddle(feedsListReqProperty, resourceList, extraInfo);
        commentService.setCommentCount(feedsList.getArticles());

        if (feedsList == null || feedsList.getArticles() == null || feedsList.getArticles().size() != 1) {
            log.warn("{} is not exist article", articleInfoReqProperty.getDocid());
            throw new BadRequestException("this article is not exist");
        }

        return feedsList.getArticles().get(0);
    }

    /**
     * 批量获取视频详情
     *
     * @param batchArticleInfoReqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<Map<String, Article>> getBatchArticleInfo(BatchArticleInfoReqProperty batchArticleInfoReqProperty) {
        FeedsListReqProperty feedsListReqProperty = new FeedsListReqProperty();
        feedsListReqProperty.setSource(FeedsConstant.TopResource.YOULI);
        feedsListReqProperty.setAttributeValues(batchArticleInfoReqProperty.getAttributeValues());
        feedsListReqProperty.setFeedssession(batchArticleInfoReqProperty.getFeedssession());
        feedsListReqProperty.setFromId(batchArticleInfoReqProperty.getFromId());
        feedsListReqProperty.setVersion(batchArticleInfoReqProperty.getVersion());

        Map<String, Article> result = new LinkedHashMap<>();//保证按照查询列表里docId顺序排序
        List<String> docIds = batchArticleInfoReqProperty.getDocIds();
        CompletableFuture<List<VideoResource>> resourceListCf = middleVideoResource.getResourceListCf(feedsListReqProperty, docIds);
        CompletableFuture<Map<String, Integer>> commentCntCf = commentService.getArticlesCmtCountAsync(docIds);
        return CompletableFuture.allOf(resourceListCf, commentCntCf).thenApply(aVoid -> {
            List<VideoResource> resourceList = getFutureIgnoreException(resourceListCf);
            Map<String, Integer> commentCnt = getFutureIgnoreException(commentCntCf);
            FeedsList feedsList = getFromContentMiddle(feedsListReqProperty, resourceList, null);
            commentService.setArticlesCmtCount(feedsList.getArticles(), commentCnt);
            if (CollectionUtils.isNotEmpty(feedsList.getArticles())) {
                for (Article article : feedsList.getArticles()) {
                    result.put(article.getId(), article);
                }
            }
            return result;
        });
    }

    /**
     * 获取媒体号详情
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public VideoMediaInfo getMediaInfo(VideoMediaInfoReqProperty reqProperty) throws BizException {
        return middleMediaResource.getMediaInfo(reqProperty);
    }

    /**
     * 获取媒体号视频列表
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<FeedsList> getMediaList(VideoMediaListReqProperty reqProperty) {
        return middleMediaResource.getMediaResourceList(reqProperty).thenCompose(resourceList -> {
            if (CollectionUtils.isEmpty(resourceList)) {
                return CompletableFuture.completedFuture(new FeedsList());
            }
            FeedsListReqProperty feedsListReqProperty = new FeedsListReqProperty();
            feedsListReqProperty.setSource(FeedsConstant.TopResource.YOULI);
            feedsListReqProperty.setFeedssession(reqProperty.getFeedssession());
            feedsListReqProperty.setVersion(reqProperty.getVersion());
            feedsListReqProperty.setFromId("media");
            FeedsList feedsList = getFromContentMiddle(feedsListReqProperty, resourceList, null);
            List<String> ids = new ArrayList<>(resourceList.size());
            resourceList.forEach(videoResource -> ids.add(videoResource.getId()));
            return commentService.getArticlesCmtCountAsync(ids).handle((commentCnt, throwable) -> {
                commentService.setArticlesCmtCount(feedsList.getArticles(), commentCnt);
                return feedsList;
            });
        });
    }

    private void writeFallbackData(FeedsListReqProperty request, FeedsList feedsList) {
        if (feedsList == null || CollectionUtils.isEmpty(feedsList.getArticles())) {
            return;
        }
        String fallbackDataKey = String.format("video:%s:%s", request.getSource(), request.getFromId());
        try {
            if (feedsList.getArticles().size() > 3) {
                log.info("************ write response data to fallbackDataKey:[{}] ************",
                        fallbackDataKey);
                feedsList.setFallback(Boolean.TRUE);
                FallbackDataUtil.writeFallbackData(fallbackDataKey, JsonTools.toJsonString(feedsList));
                feedsList.setFallback(Boolean.FALSE);
            }
        } catch (Exception e) {
            log.warn("write data to kafka exception: key:{}", fallbackDataKey, e);
        }
    }

    public FeedsList getFromContentMiddle(ListBaseReqProperty feedsListReqProperty, List<? extends VideoResource> resourceList, ExtraInfo extraInfo) {
        FeedsList feedsList = new FeedsList();
        if (MyListUtils.isEmpty(resourceList)) {
            return feedsList;
        }
        int count = 0;
        for (VideoResource videoResource : resourceList) {
            if (videoResource == null || videoResource.getResourceDetail() == null || CollectionUtils.isEmpty(videoResource.getResourceDetail().getResourceDetailList())) {
                log.warn("invalid video resource:{}, resourceList:{}, req:{}", videoResource, resourceList,
                        feedsListReqProperty);
                MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_FILTER_COUNT, 1);
                continue;
            }
            // 设置item
            Item item = new Item();
            item.setId(videoResource.getId());
            setItem(item);

            // 设置article
            Article article = new Article();
            article.setId(videoResource.getId());
            setArticle(article, videoResource, feedsListReqProperty, extraInfo);
            setRelationInfo(videoResource, article, feedsListReqProperty);
            setExtraInfo(article, extraInfo);
            setFilterWords(article, videoResource);
            // 设置详情页URL
            article.setUrl(buildVideoDetailUrl(feedsListReqProperty, videoResource));
            // 设置评论页URL
            article.setCmtUrl(buildVideoCommentUrl(feedsListReqProperty, videoResource.getId()));
            article.setIssuedReason(ResourceConfig.getRecIssuedReason());
            feedsList.addItem(item);
            feedsList.addArticle(article);
            count++;
        }
        feedsList.setNewsCount(count);
        if (feedsListReqProperty.getOffset() == null || feedsListReqProperty.getOffset() < 1) {
            feedsList.setOffset(count);
        } else {
            feedsList.setOffset(count + feedsListReqProperty.getOffset());
        }
        return feedsList;
    }

    private void setRelationInfo(VideoResource videoResource, Article article, ListBaseReqProperty request) {
        if (request.getVersion() < 52000 || videoResource == null || !(videoResource instanceof Recommend) || article == null) {
            return;
        }
        Recommend recommend = (Recommend) videoResource;
        if (CollectionUtils.isEmpty(recommend.getAlgoRelationInfoList())) {
            return;
        }
        List<Long> appIds = recommend.getAlgoRelationInfoList().stream()
                .filter(info -> info != null && "game".equals(info.getType())).map(info -> NumberUtils.toLong(info.getId()))
                .filter(appId -> appId != 0).limit(youliApiConfig.getGameNum()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appIds)) {
            return;
        }
        Map<Long, AppDetailInfo> map = null;
        try {
            map = appStoreService.queryAppInfoWithBookStatus(appIds, request, request.getAndroidVersionCode()).get();
            log.debug("appStoreService.queryAppInfoWithBookStatus, request:{}, appIds:{}, map:{}", request, appIds, map);
        } catch (Exception e) {
            log.warn("appStoreService.queryAppInfoWithBookStatus exception, request:{}, appIds:{}", request, appIds, e);
        }
        if (MapUtils.isEmpty(map)) {
            return;
        }
        for (long appId : appIds) {
            AppDetailInfo appBaseDetailDto = map.get(appId);//NOSONAR
            if (appBaseDetailDto == null) {
                continue;
            }
            RelationInfo relationInfo = buildRelationInfo(appBaseDetailDto);
            if (relationInfo == null) {
                continue;
            }
            article.setRelationList(Arrays.asList(relationInfo));
            return;
        }

//        if (CollectionUtils.isEmpty(videoResource.getRelationInfoList())) {
//            return;
//        }
//        if (EDU_LONG_VIDEO_RELATED_TYPE.equals(videoResource.getRelationInfoList().get(0).getRelatedType()) && request.getVersion().intValue() < EDU_LONG_VIDEO_SUPPORT_MIN_VERSION) {
//            return;
//        }
//        Relation relation = videoResource.getRelationInfoList().get(0).getInfo();
//        if (relation == null)
//            return;
//        RelationInfo relationInfo = new RelationInfo();
//        relationInfo.setId(relation.getSid());
//        relationInfo.setEid(relation.getEid());
//        relationInfo.setTitle(relation.getTitle());
//        relationInfo.setSource(FeedsConstant.TopResource.YOULI);
//        relationInfo.setSourceMedia(relation.getSource());
//        relationInfo.setDeeplink(relation.getDeepLinkUrl());
//        relationInfo.setCategory(relation.getChineseType());
//        relationInfo.addImage(relation.getHorizontalImage());
//        relationInfo.setYear(relation.getYear());
//        relationInfo.setArea(relation.getArea());
//        relationInfo.setSubCategory(relation.getCategory());
//        relationInfo.setContentType(RelationTypeConstant.LONG_VIDEO);
//        relationInfo.setStyleType(StyleTypeConstant.LONG_VIDEO);
//        article.setRelationList(Arrays.asList(relationInfo));
    }

    private RelationInfo buildRelationInfo(AppDetailInfo appDto) {
        if (StringUtils.isAnyBlank(appDto.getAppName(), appDto.getPkgName(), appDto.getIconUrl(), appDto.getShortDesc())) {
            log.info("filter app cause by appName、pkgName、iconUrl or shortDesc is empty. appDto={}", appDto);
            return null;
        }
        RelationInfo relationInfo = new RelationInfo();
        relationInfo.setContentType(RelationTypeConstant.APP_INFO);
        relationInfo.setId("");//字段不能空null，填充为""
        relationInfo.setStyleType(StyleTypeConstant.LONG_VIDEO);
        relationInfo.setAppInfo(new AppInfo(appDto, appStoreService.downloadToken(appDto.getPkgName())));
        relationInfo.setIssuedReason("recommend");
        return relationInfo;
    }

    private void setExtraInfo(Article article, ExtraInfo extraInfo) {
        if (extraInfo == null) {
            return;
        }
        if (extraInfo.getTaggedAuthor() != null) {
            article.setTaggedAuthor(StringUtils.defaultString(extraInfo.getTaggedAuthor().get(article.getId())));
        }
        if (extraInfo.getTaggedTitle() != null) {
            article.setTaggedTitle(StringUtils.defaultString(extraInfo.getTaggedTitle().get(article.getId())));
        }
        article.setStatisticsid(StringUtils.defaultString(extraInfo.getRid(), ""));
        article.setPageId(StringUtils.defaultString(extraInfo.getBidlst(), ""));
        //自建算法请求id放入到每个article对象中
        article.setAiRid(StringUtils.defaultString(extraInfo.getRid(), ""));
    }

    private void setItem(Item item) {
        if (item == null) {
            return;
        }
        item.setMap("articles");
    }

    private void setArticle(Article article, VideoResource videoResource, ListBaseReqProperty feedsListReqProperty, ExtraInfo extraInfo) {
        article.setTitle(StringUtils.defaultIfEmpty(videoResource.getArtificialTitle(), videoResource.getTitle()));
        article.setSource(FeedsConstant.TopResource.YOULI);
        article.setSourceMedia(videoResource.getVendor());
        Author author = videoResource.getAuthor();
        article.setSourceName(author.getName());
        article.setStyleType(videoResource.getContentType() == 2 ? StyleTypeConstant.VIDEO : StyleTypeConstant.SMALL_VIDEO);
        //5.0版本后不再设置描述默认值
        if ((StringUtils.isEmpty(videoResource.getSummary()) || videoResource.getSummary().equals(article.getTitle())) &&
                feedsListReqProperty.getVersion() < 50000) {
            article.setSummary(ResourceConfig.getDefaultSummary());
        } else {
            article.setSummary(videoResource.getSummary());
        }
        VideoCountInfo count = videoResource.getCount();
        if (count == null) {
            count = new VideoCountInfo();
            log.error("videoResource no count:{}", videoResource);
        }
        article.setDislikeCnt((int) count.getDislike());
        article.setPublishTime(videoResource.getPublishTime());
        article.setBeHotTime((long) videoResource.getPublishTime()); //以发布时间作为审核通过时间
        article.setPageId(feedsListReqProperty.getFromId());
        article.setContentType(videoResource.getContentType() == 2 ? 2 : 5);
        article.setCmtCnt(ResourceConfig.isCmtShowNum() ? (int) count.getComment() : 0);
        article.setShareCnt((int) count.getShare());
        article.setCmtUrl(""); // 评论URL
        article.setCmtNumShowType(ResourceConfig.getCmtType());  // 传给客户端评论列表h5/native通知标识
        Random random = new Random(article.getId().hashCode());
        int randomPlayCnt =
                random.nextInt(ResourceConfig.getMaxPlaycnt()) + ResourceConfig.getMinPlaycnt() + (int) count.getPlay();
        int randomLikeCnt =
                random.nextInt(ResourceConfig.getMaxLikecnt()) + ResourceConfig.getMinLikecnt() + (int) count.getLike();
        article.setViewCnt(randomPlayCnt);
        article.setLikeCnt(randomLikeCnt % (randomPlayCnt + 1));
        article.setStatisticsName(FeedsConstant.TopResource.YOULI);
        article.setOutId(videoResource.getThirdPartyId());
        article.setCategory(Arrays.asList(ArrayUtils.nullToEmpty(videoResource.getCategory())));

        //视频满足出媒体号的条件才出，不满足则不出
        if (!StringUtils.isEmpty(author.getName()) && !StringUtils.isEmpty(author.getId()) && videoResource.getPublishTime() != 0) {
            Medium medium = new Medium();
            medium.setName(author.getName());
            if (!StringUtils.isEmpty(author.getAvatar())) {
                medium.setAvatar(author.getAvatar());
            } else {
                medium.setAvatar(ResourceConfig.getDefaultAuthorAvatar());
            }
            medium.setMediaNo(author.getId());
            medium.setMediaSource(FeedsConstant.TopResource.YOULI);
            medium.setFollowStatus(true);
            article.setMedium(medium);
            article.setSourceName(medium.getName());
        } else {
            article.setSourceName(ResourceConfig.getMediaName(article.getId()));
        }

        Video video = new Video();
        setVideo(article, video, videoResource);
        List<Video> videos = new ArrayList<>();
        videos.add(video);
        article.setVideos(videos);
        String openFrom = StringUtils.EMPTY;
        if(extraInfo != null && StringUtils.isNotEmpty(extraInfo.getDpOpenFrom())){
            openFrom = extraInfo.getDpOpenFrom();
        }
        article.setDeeplink(String.format(ResourceConfig.getDeepLinkUrl(),
                videoResource.getContentType() == 2 ? "short_video" : "small_video",
                article.getId(), article.getSource(), MyHttpUtils.urlEncoder(video.getUrl()), openFrom));
    }


    private void setVideo(Article article, Video video, VideoResource videoResource) {
        if (videoResource == null || video == null || videoResource.getResourceDetail() == null) {
            return;
        }
        VideoDetailInfo videoDetailInfo = videoResource.getResourceDetail();
        List<VideoInfo> videoInfos = videoDetailInfo.getResourceDetailList();
        if (MyListUtils.isEmpty(videoInfos)) {
            return;
        }
        //播放地址按照清晰度从低到高排列
        Collections.sort(videoInfos, QualityComparable.getInstance());
        //循环处理播放地址
        for (VideoInfo videoInfo : videoInfos) {
            VideoUrl videoUrl = new VideoUrl();
            Double size = videoInfo.getSize() != 0 ? videoInfo.getSize() :
                    (long) (0.085 * videoResource.getResourceDetail().getDuration() * 1024 * 1024);
            videoUrl.setSize(size.intValue());
            videoUrl.setQuality(videoInfo.getQuality());
            //videoUrl.setUrl(getCdnPlayUrl(videoInfo.getUrl(), videoResource, videoInfo.getQuality()));
            videoUrl.setUrl(videoInfo.getUrl());
            videoUrl.setFormat(videoInfo.getFormat());
            video.addVideoUrl(videoUrl);
        }
        //默认为最高清晰度播放地址
        video.setUrl(video.getUrls().get(video.getUrls().size() - 1).getUrl());
        video.setLength((int) videoDetailInfo.getDuration());
        String img = handleImgUrl(videoResource);
        if (StringUtils.isEmpty(img)) {
            log.warn("img empty,videoResource:{}", videoResource);
        }
//        img = getCdnImgUrl(img);
        video.setImage(img);
        video.setViewCnt(article.getViewCnt());
    }

//    private String getCdnPlayUrl(String url, VideoResource videoResource, int quality) {
//        String[] ossAccessList = FeedsAccessConfig.getOssAccessList();
//        for (String ossAccess : ossAccessList) {
//            if (!url.startsWith(ossAccess)) {
//                continue;
//            }
//            return FeedsAccessConfig.getFeedsAccess() + "/video/toutiaoVideo302?source=" + FeedsConstant.TopResource.YOULI +
//                    "&videoId="
//                    + videoResource.getId() + "&authorId=" + videoResource.getAuthor().getId()
//                    + "&quality=" + quality + "&timestamp=" + StringUtils.deleteWhitespace(SimpleDateFormatHolder.getSimpleDateFormat().format(new Date()))
//                    + "&keyword=" + url.substring(ossAccess.length());
//        }
//        return url;
//    }
//
//    private String getCdnImgUrl(String url) {
//        String[] ossAccessImgList = FeedsAccessConfig.getOssAccessImgList();
//        for (String ossAccessImg : ossAccessImgList) {
//            if (!url.startsWith(ossAccessImg)) {
//                continue;
//            }
//            return FeedsAccessConfig.getCdnAccessImg() + url.substring(ossAccessImg.length());
//        }
//        return url;
//    }

    private String buildVideoDetailUrl(ListBaseReqProperty feedsListReqProperty, VideoResource resource) {
        String url = org.apache.commons.lang.StringUtils.replace(ResourceConfig.getVideoDetailUrl(), "${docId}", resource.getId());
        // 拼getArticleInfo接口需要的参数；source和docid是必须的
        StringBuilder sb = StringBuilderHolder.getStringBuilder();
        sb.append(url);
        sb.append("?__t=");
        sb.append(System.currentTimeMillis() / 1000);
        sb.append("&__docid__=");
        sb.append(resource.getId());
        sb.append("&feedssession=");
        sb.append(feedsListReqProperty.getFeedssession());
        sb.append("&__fromId__=");
        sb.append(feedsListReqProperty.getFromId());
        sb.append("&enterId=");
        sb.append(feedsListReqProperty.getEnterId());
        sb.append("&__source__=");
        sb.append(FeedsConstant.TopResource.YOULI);
        sb.append("&statisticsid=");
        sb.append(feedsListReqProperty.getStatisticsid());
        sb.append("&version=");
        sb.append(feedsListReqProperty.getVersion());
        sb.append("&mediaNo=");
        sb.append(resource.getAuthor().getId());
        return sb.toString();
    }


    private String buildVideoCommentUrl(ListBaseReqProperty feedsListReqProperty, String docId) {
        String url = StringUtils.replace(ResourceConfig.getVideoCommentUrl(), "${docId}", docId);
        // 拼getArticleInfo接口需要的参数；source和docid是必须的
        StringBuilder sb = StringBuilderHolder.getStringBuilder();
        sb.append(url);
        sb.append("?__t=");
        sb.append(System.currentTimeMillis() / 1000);
        sb.append("&__docid__=");
        sb.append(docId);
        sb.append("&feedssession=");
        sb.append(feedsListReqProperty.getFeedssession());
        sb.append("&__fromId__=");
        sb.append(feedsListReqProperty.getFromId());
        sb.append("&__source__=");
        sb.append(FeedsConstant.TopResource.YOULI);
        sb.append("&statisticsid=");
        sb.append(feedsListReqProperty.getStatisticsid());
        sb.append("&version=");
        sb.append(feedsListReqProperty.getVersion());
        sb.append(ResourceConfig.getCmtAppendUrl());
        return sb.toString();
    }

    private void setFilterWords(Article article, VideoResource videoResource) {
        if (article == null || videoResource == null || !(videoResource instanceof Recommend)) {
            return;
        }
        Recommend recommend = (Recommend) videoResource;
        List<FilterWord> filterWords = recommend.getDislikeTags();
        if (CollectionUtils.isEmpty(filterWords)) {
            return;
        }
        List<ReasonObj> reasonObjs = new ArrayList<>();
        for (FilterWord filterWord : filterWords) {
            ReasonObj reasonObj = new ReasonObj();
            reasonObj.setId(filterWord.getId());
            reasonObj.setName(filterWord.getName());
            reasonObj.setSelected(false);
            reasonObjs.add(reasonObj);
        }
        article.setFilterWords(reasonObjs);
    }

    private String handleImgUrl(VideoResource videoResource) {
        if (CollectionUtils.isNotEmpty(videoResource.getSmartPicList()) && videoResource.getSmartPicList().get(0) != null
                && StringUtils.isNotEmpty(videoResource.getSmartPicList().get(0).getPicUrl())) {
            return videoResource.getSmartPicList().get(0).getPicUrl();
        }

        if (CollectionUtils.isNotEmpty(videoResource.getArtificialViewPics()) && StringUtils.isNotEmpty(videoResource.getArtificialViewPics().get(0).getPicUrl())) {
            return videoResource.getArtificialViewPics().get(0).getPicUrl();
        }
        String originUrl = "";
        if (CollectionUtils.isNotEmpty(videoResource.getCoverList())) {
            originUrl = videoResource.getCoverList().get(0).getPicUrl();
        }
        return originUrl;
    }

    /**
     * 获取内容池视频列表
     *
     * <p>处理内容池视频请求，将请求转换为标准的视频列表请求格式。</p>
     *
     * @param nRequest 内容池视频请求属性
     * @return 视频列表响应的CompletableFuture
     */
    public CompletableFuture<VideoFeedsListRt> getPondVideos(GetPondVideosReqProperty nRequest) {
        return middlePondResource.getPondVideos(nRequest).thenCompose(pondVideos -> {
            FeedsListReqProperty feedsListReqProperty = new FeedsListReqProperty();
            feedsListReqProperty.setSource(FeedsConstant.TopResource.YOULI);
            feedsListReqProperty.setFeedssession(nRequest.getFeedssession());
            feedsListReqProperty.setVersion(nRequest.getVersion());
            feedsListReqProperty.setAttributeValues(nRequest.getAttributeValues());
            return handlePondVideos(feedsListReqProperty, pondVideos, false);
        });
    }

    // ========== getFeedsList 重构后的子方法 ==========

    /**
     * 判断是否为中台内容池频道请求
     *
     * <p>检查请求的fromId是否在中台内容池频道映射中。
     * 该方法是重构后提取的私有方法，用于简化频道类型判断逻辑。</p>
     *
     * @param reqProperty 请求属性对象
     * @return true 如果是中台内容池频道请求，false 否则
     */
    private boolean isPondChannelRequest(FeedsListReqProperty reqProperty) {
        return MiddlePondResource.pondChannelPondIdMap.containsKey(reqProperty.getFromId());
    }

    /**
     * 处理中台内容池频道请求
     *
     * <p>直接获取中台内容池的频道视频数据。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 视频列表响应的CompletableFuture
     */
    private CompletableFuture<VideoFeedsListRt> handlePondChannelRequest(FeedsListReqProperty reqProperty) {
        return middlePondResource.getPondChannelVideos(reqProperty)
                .thenCompose(pondVideos -> handlePondVideos(reqProperty, pondVideos, true));
    }

    /**
     * 判断是否为长视频预告推荐请求
     *
     * <p>检查请求的fromId是否匹配长视频预告推荐算法的fromId。</p>
     *
     * @param reqProperty 请求属性对象
     * @return true 如果是长视频预告推荐请求，false 否则
     */
    private boolean isTrailerRecommendRequest(FeedsListReqProperty reqProperty) {
        return reqProperty.getFromId().equals(longVideoTrailerRecommendConfig.getRecommendAlgorithmFromId());
    }

    /**
     * 处理长视频预告推荐请求
     *
     * <p>处理自荐周边视频算法推荐，包括算法类型判断、数据处理和兜底逻辑。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 视频列表响应的CompletableFuture
     */
    private CompletableFuture<VideoFeedsListRt> handleTrailerRecommendRequest(FeedsListReqProperty reqProperty) {
        return lvtRecommendService.getRecommendAlgorithmDatas(reqProperty).thenCompose(response ->
                longVideoResource.handleResponse(reqProperty, response).thenCompose(feedsListRt -> {
                    Integer algorithmType = determineTrailerAlgorithmType(reqProperty);
                    return processAlgorithmResponse(reqProperty, response, feedsListRt, algorithmType,
                            "trailerRecommendPool", longVideoResource::handleResponse);
                }));
    }

    /**
     * 确定长视频预告算法类型
     *
     * <p>根据请求参数确定使用哪种算法类型：</p>
     * <ul>
     *   <li>有sid和vid且非沉浸式场景：算法类型一</li>
     *   <li>有内容池code：算法类型二</li>
     *   <li>其他情况：null（非算法场景）</li>
     * </ul>
     *
     * @param reqProperty 请求属性对象
     * @return 算法类型，如果不是算法场景则返回null
     */
    private Integer determineTrailerAlgorithmType(FeedsListReqProperty reqProperty) {
        if (hasValidSidAndVid(reqProperty)) {
            if (isImmersionTrailerScene(reqProperty)) {
                // 详情页周边视频进入沉浸式，将周边视频列表通过getList接口下发
                return null;
            } else {
                return longVideoConfig.getAlgorithmTypeOne();
            }
        } else if (StringUtils.isNotEmpty(reqProperty.getPoolCode())) {
            // 内容池周边视频推荐 B1655276899006 场景
            return longVideoConfig.getAlgorithmTypeTwo();
        }
        return null;
    }

    /**
     * 检查是否有有效的sid和vid
     *
     * <p>验证请求中的sid和vid是否都不为空。</p>
     *
     * @param reqProperty 请求属性对象
     * @return true 如果sid和vid都不为空，false 否则
     */
    private boolean hasValidSidAndVid(FeedsListReqProperty reqProperty) {
        return StringUtils.isNotEmpty(reqProperty.getSid()) && StringUtils.isNotEmpty(reqProperty.getVid());
    }

    /**
     * 判断是否为沉浸式预告场景
     *
     * <p>检查是否满足沉浸式预告场景的所有条件：</p>
     * <ul>
     *   <li>沉浸式预告场景标识为1</li>
     *   <li>预告操作码不为空</li>
     *   <li>原始频道不为null</li>
     * </ul>
     *
     * @param reqProperty 请求属性对象
     * @return true 如果是沉浸式预告场景，false 否则
     */
    private boolean isImmersionTrailerScene(FeedsListReqProperty reqProperty) {
        return reqProperty.getImmersionTrailerScene() != null &&
               reqProperty.getImmersionTrailerScene() == 1 &&
               StringUtils.isNotBlank(reqProperty.getTrailerOperationCode()) &&
               reqProperty.getOriginalChannel() != null;
    }

    /**
     * 判断是否为UGC请求
     *
     * <p>检查请求的fromId是否为"ugc"（不区分大小写）。</p>
     *
     * @param reqProperty 请求属性对象
     * @return true 如果是UGC请求，false 否则
     */
    private boolean isUgcRequest(FeedsListReqProperty reqProperty) {
        return "ugc".equalsIgnoreCase(reqProperty.getFromId());
    }

    /**
     * 处理UGC请求
     *
     * <p>处理芒果UGC视频算法推荐，包括默认内容池设置、算法请求和兜底逻辑。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 视频列表响应的CompletableFuture
     */
    private CompletableFuture<VideoFeedsListRt> handleUgcRequest(FeedsListReqProperty reqProperty) {
        // 设置默认UGC内容池
        if (StringUtils.isBlank(reqProperty.getPoolCode())) {
            reqProperty.setPoolCode(longVideoTrailerRecommendConfig.getDefaultUgcPool());
        }

        return lvtRecommendService.requestAlgorithm(reqProperty, longVideoConfig.getAlgorithmTypeUGC())
                .thenCompose(response -> longVideoResource.handleUgc(reqProperty, response)
                        .thenCompose(feedsListRt -> processAlgorithmResponse(reqProperty, response, feedsListRt,
                                longVideoConfig.getAlgorithmTypeUGC(), "ugcPool", longVideoResource::handleUgc)));
    }

    /**
     * 判断是否为风行短视频请求
     *
     * <p>检查请求的fromId是否为"fengxingShort"（不区分大小写）。</p>
     *
     * @param reqProperty 请求属性对象
     * @return true 如果是风行短视频请求，false 否则
     */
    private boolean isFengxingShortRequest(FeedsListReqProperty reqProperty) {
        return "fengxingShort".equalsIgnoreCase(reqProperty.getFromId());
    }

    /**
     * 处理风行短视频请求
     *
     * <p>处理风行短视频算法推荐，包括版本检查、默认内容池设置、算法请求和兜底逻辑。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 视频列表响应的CompletableFuture
     */
    private CompletableFuture<VideoFeedsListRt> handleFengxingShortRequest(FeedsListReqProperty reqProperty) {
        // 版本检查：低于7.13版本不下发风行短视频内容
        if (reqProperty.getVersion() < 71300) {
            return CompletableFuture.completedFuture(null);
        }

        // 设置默认风行短视频内容池
        if (StringUtils.isBlank(reqProperty.getPoolCode())) {
            reqProperty.setPoolCode(longVideoTrailerRecommendConfig.getDefaultFxsPool());
        }

        return lvtRecommendService.requestAlgorithm(reqProperty, longVideoConfig.getAlgorithmTypeFXS())
                .thenCompose(response -> longVideoResource.handleFxShort(reqProperty, response)
                        .thenCompose(feedsListRt -> processAlgorithmResponse(reqProperty, response, feedsListRt,
                                longVideoConfig.getAlgorithmTypeFXS(), "fxShortPool", longVideoResource::handleFxShort)));
    }

    /**
     * 处理默认推荐请求
     *
     * <p>处理默认的推荐请求，使用推荐服务获取推荐内容。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 视频列表响应的CompletableFuture
     */
    private CompletableFuture<VideoFeedsListRt> handleDefaultRecommendRequest(FeedsListReqProperty reqProperty) {
        return recommendService.getFeedsRecommend(reqProperty)
                .thenApply(response -> handleRecommendFallbackResponse(reqProperty, response));
    }

    /**
     * 处理算法响应的通用方法
     *
     * <p>这是一个通用的算法响应处理方法，用于处理各种算法类型的响应，包括：</p>
     * <ul>
     *   <li>非算法场景的直接返回</li>
     *   <li>数据正常时的缓存更新和返回</li>
     *   <li>数据解析失败时的兜底缓存处理</li>
     * </ul>
     *
     * <h3>处理流程：</h3>
     * <ol>
     *   <li>检查算法类型，非算法场景直接返回</li>
     *   <li>检查数据有效性，有效则更新缓存并返回</li>
     *   <li>检查是否已是兜底数据，是则直接返回</li>
     *   <li>尝试获取兜底缓存数据</li>
     *   <li>处理兜底数据或返回原始结果</li>
     * </ol>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>消除了三个算法类型处理中的重复代码</li>
     *   <li>提供了统一的算法响应处理逻辑</li>
     *   <li>降低了主方法的圈复杂度</li>
     *   <li>提高了代码的一致性和可维护性</li>
     * </ul>
     *
     * @param reqProperty 请求属性对象
     * @param response 算法响应对象
     * @param feedsListRt 处理后的视频列表响应
     * @param algorithmType 算法类型
     * @param poolName 内容池名称，用于日志记录
     * @param fallbackHandler 兜底处理函数，用于处理兜底缓存数据
     * @return 最终的视频列表响应CompletableFuture
     */
    private CompletableFuture<VideoFeedsListRt> processAlgorithmResponse(
            FeedsListReqProperty reqProperty,
            LvtRecommendAlgorithmResponse response,
            VideoFeedsListRt feedsListRt,
            Integer algorithmType,
            String poolName,
            java.util.function.BiFunction<FeedsListReqProperty, LvtRecommendAlgorithmResponse, CompletableFuture<VideoFeedsListRt>> fallbackHandler) {

        // 非算法场景直接返回
        if (algorithmType == null) {
            return CompletableFuture.completedFuture(feedsListRt);
        }

        // 数据正常直接返回
        if (isValidFeedsListResponse(feedsListRt)) {
            // 非兜底数据更新缓存
            if (isNonFallbackResponse(response)) {
                algorithmPoolCache.addAlgorithmCacheMember(reqProperty, algorithmType, response);
            }
            return CompletableFuture.completedFuture(feedsListRt);
        }

        // 数据解析失败走兜底缓存
        // 如果已经是兜底数据，不再重复兜底
        if (isFallbackResponse(response)) {
            return CompletableFuture.completedFuture(feedsListRt);
        }

        // 获取缓存数据
        LvtRecommendAlgorithmResponse algorithmCacheResponse = algorithmPoolCache.getAlgorithmCacheResponse(
                reqProperty, algorithmType, FALLBACK_TYPE_CONVERT_ERROR);

        if (hasValidCacheData(algorithmCacheResponse)) {
            return fallbackHandler.apply(reqProperty, algorithmCacheResponse);
        } else {
            log.warn("[{}] getFallbackData is null, request:{}", poolName, reqProperty);
        }

        return CompletableFuture.completedFuture(feedsListRt);
    }

    /**
     * 检查视频列表响应是否有效
     *
     * <p>验证视频列表响应是否包含有效的长视频数据。</p>
     *
     * @param feedsListRt 视频列表响应
     * @return true 如果响应有效且包含长视频数据，false 否则
     */
    private boolean isValidFeedsListResponse(VideoFeedsListRt feedsListRt) {
        return feedsListRt != null &&
               feedsListRt.getData() != null &&
               CollectionUtils.isNotEmpty(feedsListRt.getData().getLongVideos());
    }

    /**
     * 检查是否为非兜底响应
     *
     * <p>验证算法响应是否不是兜底数据。</p>
     *
     * @param response 算法响应
     * @return true 如果不是兜底响应，false 如果是兜底响应或响应为null
     */
    private boolean isNonFallbackResponse(LvtRecommendAlgorithmResponse response) {
        return response != null && !response.isFallback();
    }

    /**
     * 检查是否为兜底响应
     *
     * <p>验证算法响应是否是兜底数据。</p>
     *
     * @param response 算法响应
     * @return true 如果是兜底响应，false 否则
     */
    private boolean isFallbackResponse(LvtRecommendAlgorithmResponse response) {
        return response != null && response.isFallback();
    }

    /**
     * 检查缓存数据是否有效
     *
     * <p>验证算法缓存响应是否包含有效数据。</p>
     *
     * @param algorithmCacheResponse 算法缓存响应
     * @return true 如果缓存数据有效，false 否则
     */
    private boolean hasValidCacheData(LvtRecommendAlgorithmResponse algorithmCacheResponse) {
        return algorithmCacheResponse != null && CollectionUtils.isNotEmpty(algorithmCacheResponse.getData());
    }

}
