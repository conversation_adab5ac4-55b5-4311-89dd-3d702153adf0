package com.heytap.video.youli.biz.algorithm;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.LvTrailerOperationRpcApi;
import com.heytap.longvideo.client.arrange.entity.LvTrailerOperationItem;
import com.heytap.longvideo.client.arrange.model.response.TrailerOperationBO;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.StandardTrailerRpcApi;
import com.heytap.longvideo.client.media.StandardVideoRpcApi;
import com.heytap.longvideo.client.media.VirtualProgramRelationRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.StandardTrailer;
import com.heytap.longvideo.client.media.entity.StandardVideo;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.client.media.query.PageRequest;
import com.heytap.longvideo.client.media.query.TrailerExample;
import com.heytap.video.youli.cache.AlgorithmPoolCache;
import com.heytap.video.youli.config.LongVideoConfig;
import com.heytap.video.youli.config.LongVideoTrailerRecommendConfig;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmData;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmResponse;
import com.heytap.video.youli.utils.FutureUtil;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.cpc.video.framework.lib.programsource.SourceVersionService;
import esa.rpc.common.context.RpcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.heytap.video.youli.cache.AlgorithmPoolCache.FALLBACK_TYPE_HTTP_ERROR;

/**
 * <AUTHOR> Yanping
 * @date 2022/10/12 15:54
 */
@Slf4j
@Service
public class LongVideoTrailerRecommendService {

    private final HttpDataChannel httpDataChannel;

    private final LongVideoTrailerRecommendConfig longVideoTrailerRecommendConfig;
    
    private final StandardAlbumRpcApi albumRpcApi;

    private final StandardVideoRpcApi videoRpcApi;
    
    private final VirtualProgramRelationRpcApi virtualProgramRelationRpcApi;
    
    private final LvTrailerOperationRpcApi lvTrailerOperationRpcApi;
    
    private final StandardTrailerRpcApi standardTrailerRpcApi;

    private final LongVideoConfig longVideoConfig;

    private final SourceVersionService sourceVersionService;

    private final AlgorithmPoolCache algorithmPoolCache;

    public LongVideoTrailerRecommendService(HttpDataChannel httpDataChannel, 
                                            LongVideoTrailerRecommendConfig longVideoTrailerRecommendConfig,
                                            @Qualifier("standardAlbumRpcApi") StandardAlbumRpcApi albumRpcApi,
                                            @Qualifier("standardVideoRpcApi") StandardVideoRpcApi videoRpcApi,
                                            @Qualifier("virtualProgramRelationRpcApi") VirtualProgramRelationRpcApi virtualProgramRelationRpcApi,
                                            @Qualifier("lvTrailerOperationRpcApi") LvTrailerOperationRpcApi lvTrailerOperationRpcApi,
                                            @Qualifier("standardTrailerRpcApi") StandardTrailerRpcApi standardTrailerRpcApi, 
                                            LongVideoConfig longVideoConfig, 
                                            SourceVersionService sourceVersionService, 
                                            AlgorithmPoolCache algorithmPoolCache) {
        this.httpDataChannel = httpDataChannel;
        this.longVideoTrailerRecommendConfig = longVideoTrailerRecommendConfig;
        this.albumRpcApi = albumRpcApi;
        this.videoRpcApi = videoRpcApi;
        this.virtualProgramRelationRpcApi = virtualProgramRelationRpcApi;
        this.lvTrailerOperationRpcApi = lvTrailerOperationRpcApi;
        this.standardTrailerRpcApi = standardTrailerRpcApi;
        this.longVideoConfig = longVideoConfig;
        this.sourceVersionService = sourceVersionService;
        this.algorithmPoolCache = algorithmPoolCache;
    }

    public CompletableFuture<LvtRecommendAlgorithmResponse> getRecommendAlgorithmDatas(FeedsListReqProperty reqProperty) {
        //周边视频推荐流	 B1655276899003	 场景
        if (StringUtils.isNotEmpty(reqProperty.getSid()) && StringUtils.isNotEmpty(reqProperty.getVid())) {
            if (reqProperty.getImmersionTrailerScene() != null && reqProperty.getImmersionTrailerScene() == 1 &&
                    StringUtils.isNotBlank(reqProperty.getTrailerOperationCode()) && reqProperty.getOriginalChannel() != null) {
                //详情页周边视频进入沉浸式，将周边视频列表通过getList接口下发
                return getDetailPageTrailerData(reqProperty);
            }
            return requestAlgorithm(reqProperty, longVideoConfig.getAlgorithmTypeOne());
        } else if (StringUtils.isNotEmpty(reqProperty.getPoolCode())) { //内容池周边视频推荐 B1655276899006 场景
            return requestAlgorithm(reqProperty, longVideoConfig.getAlgorithmTypeTwo());
        } else {
            log.warn("sid or vid or poolCode is null, reqProperty:{}", reqProperty);
            return CompletableFuture.completedFuture(null);
        }
    }


    /**
     * 获取周边视频数据，保持和详情页下发的周边视频数据一致
     * @param reqProperty
     * @return
     */
    private CompletableFuture<LvtRecommendAlgorithmResponse> getDetailPageTrailerData(FeedsListReqProperty reqProperty) {

        //1、获取人工渠道配置的周边视频数据.
        if (reqProperty.getOriginalChannel() == 0) {
            log.debug("operationChannel, sid:{}, trailerOperationCode:{}", reqProperty.getSid(), reqProperty.getTrailerOperationCode());
            HashSet<String> hashSet = new HashSet<>();
            hashSet.add(reqProperty.getTrailerOperationCode());
            return queryTrailerOperationData(hashSet).handle(((vidList, e) ->
                    handle2CommonResponse(reqProperty.getSid(), reqProperty.getInsertVid(), vidList)));
        }

        //2、详情页只下发了媒资的周边视频数据(此时详情页下发的周边视频模块code以default开头)
        if (reqProperty.getTrailerOperationCode().contains("default_")) {
            log.debug("default originalChannel, sid:{}, trailerOperationCode:{}", reqProperty.getSid(), reqProperty.getTrailerOperationCode());
            return queryStandardTrailersBy(reqProperty.getSid()).handle((vidList, e)->
                    handle2CommonResponse(reqProperty.getSid(), reqProperty.getInsertVid(), vidList));
        }

        //3、详情页同时下发了人工配置和媒资的周边视频数据，此时需要将媒资周边视频模块的数据进行去重
        return handleRepeatedMediaDta(reqProperty);
    }

    private CompletableFuture<LvtRecommendAlgorithmResponse> handleRepeatedMediaDta(FeedsListReqProperty reqProperty) {
        //详情页同时下发了人工配置和媒资的周边视频数据，此时需要将媒资周边视频模块的数据进行去重
        HashSet<String> hashSet = new HashSet<>();
        if (StringUtils.isNotBlank(reqProperty.getOtherTrailerOperationCodes())) {
            String[] codesArray = reqProperty.getOtherTrailerOperationCodes().split(",");
            hashSet.addAll(new HashSet<>(Arrays.asList(codesArray)));
        }
        //分别查询原始媒资周边视频列表和人工配置的周边视频列表
        CompletableFuture<List<String>> mediaDataCF = queryStandardTrailersBy(reqProperty.getSid());
        CompletableFuture<List<String>> operationDataCF = queryTrailerOperationData(hashSet);

        return CompletableFuture.allOf(mediaDataCF, operationDataCF).handle((aVoid, e)->{
            if (e != null) {
                log.error("mediaData and standardTrailerList is empty, sid:{}, OtherTrailerOperationCodes:{}", reqProperty.getSid(), reqProperty.getOtherTrailerOperationCodes());
                return null;
            }
            List<String> mediaVidList = FutureUtil.getFutureIgnoreException(mediaDataCF);
            List<String> operationVidList = FutureUtil.getFutureIgnoreException(operationDataCF);
            if (CollectionUtils.isEmpty(mediaVidList)) {
                log.error("mediaData standardTrailerList is empty, sid:{}", reqProperty.getSid());
                return null;
            }

            if (CollectionUtils.isEmpty(operationVidList)) {
                return handle2CommonResponse(reqProperty.getSid(), reqProperty.getInsertVid(), mediaVidList);
            }
            //原始媒资周边视频去重
            mediaVidList.removeIf(operationVidList::contains);
            return handle2CommonResponse(reqProperty.getSid(), reqProperty.getInsertVid(), mediaVidList);
        });
    }


    private CompletableFuture<List<String>> queryTrailerOperationData(HashSet<String> hashSet) {
        try {
            RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
            return lvTrailerOperationRpcApi.findLvTrailerOperationSetByCodes(hashSet)
                    .handle((rpcResult, e)->{
                        if (e != null || rpcResult == null || rpcResult.getCode() != 0 || CollectionUtils.isEmpty(rpcResult.getData())) {
                            log.error("lvTrailerOperationRpcApi.findLvTrailerOperationSetByCodes error , codes:{}, error msg:", JSON.toJSONString(hashSet), e);
                            return Collections.emptyList();
                        }
                        // 使用iterator()方法获取HashSet的迭代器
                        Iterator<TrailerOperationBO> iterator = rpcResult.getData().iterator();
                        //结果
                        List<LvTrailerOperationItem> lvTrailerOperationItemList = new ArrayList<>();
                        while (iterator.hasNext()) {
                            TrailerOperationBO trailerOperationBO = iterator.next();
                            if (trailerOperationBO != null && CollectionUtils.isNotEmpty(trailerOperationBO.getItems())) {
                                lvTrailerOperationItemList.addAll(trailerOperationBO.getItems());
                            }
                        }
                        return lvTrailerOperationItemList.stream().map(LvTrailerOperationItem::getVid).collect(Collectors.toList());
                    });
        }catch (Exception e) {
            log.error("lvTrailerOperationRpcApi.findLvTrailerOperationSetByCodes error , codes:{}, error msg:", JSON.toJSONString(hashSet), e);
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

    }

    /**
     * 批量查询周边视频列表
     * @param sid
     * @return
     */
    private CompletableFuture<List<String>> queryStandardTrailersBy(String sid) {
        TrailerExample trailerExample = new TrailerExample();
        trailerExample.setStatus(1);
        trailerExample.setSid(sid);
        PageRequest pageRequest = new PageRequest(0, 35);
        try {
            return standardTrailerRpcApi.queryBy(trailerExample, pageRequest).handle((rpcResult, e)->{
                if (e != null || rpcResult == null || rpcResult.getCode() != 0 || CollectionUtils.isEmpty(rpcResult.getData())) {
                    log.error("standardTrailerRpcApi.queryBy error , sid:{}, ", sid, e);
                    return Collections.emptyList();
                }
                return rpcResult.getData().stream().map(StandardTrailer::getVid).collect(Collectors.toList());
            });
        }catch (Exception e) {
            log.error("queryStandardTrailersBy error, error msg:", e);
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

    }

    private LvtRecommendAlgorithmResponse handle2CommonResponse(String sid, String insertVid, List<String> vidList) {
        //构建统一返回
        List<LvtRecommendAlgorithmData> data = new ArrayList<>();

        //当列表页配置了起播类型为周边视频的节目时，并且用户点击的周边视频所在模块为第一个周边视频模块，请求参数的insertVid为该周边视频
        //这时将其插入到周边视频列表的第一个位置
        if (StringUtils.isNotBlank(insertVid) && CollectionUtils.isNotEmpty(vidList) && !vidList.contains(insertVid)) {
            vidList.add(0, insertVid);
        }

        if (CollectionUtils.isNotEmpty(vidList)) {
            log.debug("standardTrailerList is not empty, sid:{}", sid);
            for (String videoId : vidList) {
                LvtRecommendAlgorithmData lvtRecommendAlgorithmData = new LvtRecommendAlgorithmData();
                lvtRecommendAlgorithmData.setId(sid);
                lvtRecommendAlgorithmData.setVideoId(videoId);
                data.add(lvtRecommendAlgorithmData);
            }
        }
        return buildCommonResponse(data);
    }

    private LvtRecommendAlgorithmResponse buildCommonResponse(List<LvtRecommendAlgorithmData> data) {

        LvtRecommendAlgorithmResponse response = new LvtRecommendAlgorithmResponse();
        response.setStatus(0);
        response.setData(data);
        response.setNum(data.size());
        return response;
    }

    /**
     * 请求算法推荐接口
     *
     * <p>该方法是重构后的主方法，通过调用多个子方法来构建完整的算法请求参数并执行请求。
     * 重构前该方法的圈复杂度为20，重构后降低到8以下。</p>
     *
     * <h3>重构策略：</h3>
     * <ul>
     *   <li>方法分解（Extract Method）- 将大方法分解为多个小方法</li>
     *   <li>条件合并（Consolidate Conditional Expression）- 合并相似的算法类型判断</li>
     *   <li>复杂条件提取（Extract Method for Complex Conditions）- 提取复杂条件判断</li>
     *   <li>逻辑分层 - 按功能职责将参数构建分为多个层次</li>
     * </ul>
     *
     * <h3>参数构建流程：</h3>
     * <ol>
     *   <li>构建基础参数 - 设备信息、路由、业务ID等</li>
     *   <li>添加算法类型特定参数 - 根据不同算法类型添加特定参数</li>
     *   <li>添加通用请求参数 - 数量、用户ID、页面信息等</li>
     *   <li>确定算法URL - 根据算法类型选择合适的URL</li>
     *   <li>添加版本特定参数 - 根据客户端版本添加源列表</li>
     *   <li>执行算法请求 - 发送HTTP请求并处理响应</li>
     * </ol>
     *
     * @param reqProperty 请求属性对象，包含所有请求相关信息
     * @param algorithmType 算法类型，决定使用哪种推荐策略
     * @return CompletableFuture包装的算法推荐响应对象
     */
    public CompletableFuture<LvtRecommendAlgorithmResponse> requestAlgorithm(FeedsListReqProperty reqProperty, Integer algorithmType) {
        Map<String, String> params = buildBaseParams(reqProperty, algorithmType);
        addAlgorithmTypeSpecificParams(params, reqProperty, algorithmType);
        addCommonRequestParams(params, reqProperty);

        String algorithmUrl = determineAlgorithmUrl(algorithmType);
        addVersionSpecificParams(params, reqProperty, algorithmType);

        return executeAlgorithmRequest(algorithmUrl, params, reqProperty, algorithmType);
    }

    /**
     * 构建基础请求参数
     *
     * <p>构建算法请求的基础参数，包括设备信息、路由配置、业务ID和推荐栏ID等。
     * 这些参数是所有算法请求都必须包含的核心参数。</p>
     *
     * @param reqProperty 请求属性对象，包含设备信息等
     * @param algorithmType 算法类型，用于获取对应的推荐栏ID
     * @return 包含基础参数的Map
     */
    private Map<String, String> buildBaseParams(FeedsListReqProperty reqProperty, Integer algorithmType) {
        Map<String, String> params = new HashMap<>();
        params.put("r_dv", reqProperty.getAttributeValues().getPhone());
        params.put("route", longVideoTrailerRecommendConfig.getRecommendAlgorithmRoute());
        params.put("cid", longVideoTrailerRecommendConfig.getRecommendAlgorithmCid());
        params.put("bidlst", longVideoTrailerRecommendConfig.getBidList().get(algorithmType));
        return params;
    }

    /**
     * 添加算法类型特定参数
     *
     * <p>根据不同的算法类型添加特定的参数。算法类型一需要频道ID、文档ID和视频ID，
     * 其他内容池算法类型需要内容池ID。</p>
     *
     * <h3>算法类型说明：</h3>
     * <ul>
     *   <li>算法类型一：需要频道ID、文档ID、视频ID</li>
     *   <li>内容池算法类型：需要内容池ID（包括类型二、UGC、FXS）</li>
     * </ul>
     *
     * @param params 参数Map，用于添加特定参数
     * @param reqProperty 请求属性对象，包含相关ID信息
     * @param algorithmType 算法类型
     */
    private void addAlgorithmTypeSpecificParams(Map<String, String> params, FeedsListReqProperty reqProperty, Integer algorithmType) {
        if (longVideoConfig.getAlgorithmTypeOne().equals(algorithmType)) {
            params.put("r_channel_id", longVideoTrailerRecommendConfig.getRecommendAlgorithmChannelId());
            params.put("docId", reqProperty.getSid());
            params.put("video_id", reqProperty.getVid());
        } else if (isContentPoolAlgorithmType(algorithmType)) {
            params.put("r_content_pool_id", reqProperty.getPoolCode());
        }
    }

    /**
     * 判断是否为内容池算法类型
     *
     * <p>内容池算法类型包括算法类型二、UGC算法类型和FXS算法类型。
     * 该方法将原来的复杂条件判断合并，降低了圈复杂度。</p>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>合并了相似的算法类型判断逻辑</li>
     *   <li>提高了代码的可读性和可维护性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @param algorithmType 算法类型
     * @return true 如果是内容池算法类型，false 否则
     */
    private boolean isContentPoolAlgorithmType(Integer algorithmType) {
        return longVideoConfig.getAlgorithmTypeTwo().equals(algorithmType) ||
               longVideoConfig.getAlgorithmTypeUGC().equals(algorithmType) ||
               longVideoConfig.getAlgorithmTypeFXS().equals(algorithmType);
    }

    /**
     * 添加通用请求参数
     *
     * <p>添加所有算法请求都需要的通用参数，包括视频数量、用户ID、页面信息等。</p>
     *
     * @param params 参数Map，用于添加通用参数
     * @param reqProperty 请求属性对象，包含相关信息
     */
    private void addCommonRequestParams(Map<String, String> params, FeedsListReqProperty reqProperty) {
        int num = calculateVideoNum(reqProperty);
        params.put("num", String.valueOf(num));

        long buuid = extractBuuid(reqProperty);
        params.put("r_buuid", String.valueOf(buuid));
        params.put("r_page", String.valueOf(reqProperty.getRefreshTimes()));
        params.put("r_channel_name", String.valueOf(reqProperty.getLvChannelName()));
        params.put("r_page_id", String.valueOf(reqProperty.getPageCode()));
    }

    /**
     * 计算视频数量
     *
     * <p>根据是否开启获取更多视频开关和内容数量配置来确定请求的视频数量。
     * 如果开启了获取更多视频开关且配置了内容数量，则使用配置的数量，否则使用默认限制。</p>
     *
     * <h3>业务逻辑：</h3>
     * <ul>
     *   <li>混排场景需要获取更多视频以提供混排空间</li>
     *   <li>优先使用运营配置的每页视频数量</li>
     *   <li>未配置时使用默认的限制数量</li>
     * </ul>
     *
     * @param reqProperty 请求属性对象
     * @return 计算得出的视频数量
     */
    private int calculateVideoNum(FeedsListReqProperty reqProperty) {
        return (reqProperty.isFetchMoreVideosSwitch() && reqProperty.getContentCount() != null)
                ? reqProperty.getContentCount() : reqProperty.getLimit();
    }

    /**
     * 提取用户唯一标识
     *
     * <p>从请求属性中提取用户唯一标识（buuid）。如果属性值中的buuid为0，
     * 则尝试从Cookie中获取。该方法将原来复杂的条件判断分层处理。</p>
     *
     * <h3>提取优先级：</h3>
     * <ol>
     *   <li>请求属性中的buuid（如果不为0）</li>
     *   <li>Cookie中的buuid（如果Cookie有效）</li>
     * </ol>
     *
     * @param reqProperty 请求属性对象
     * @return 用户唯一标识
     */
    private long extractBuuid(FeedsListReqProperty reqProperty) {
        AttributeValues attributeValues = reqProperty.getAttributeValues();
        long buuid = attributeValues.getBuuid();

        if (buuid == 0) {
            Cookie cookie = reqProperty.getScookieIgnoreException();
            if (isCookieBuuidValid(cookie)) {
                buuid = cookie.getInfo().getBuuid();
            }
        }
        return buuid;
    }

    /**
     * 验证Cookie中buuid的有效性
     *
     * <p>检查Cookie对象及其Info对象和buuid字段是否都不为null。
     * 该方法将复杂的 && 条件判断提取为独立方法。</p>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 && 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @param cookie Cookie对象
     * @return true 如果Cookie中的buuid有效，false 否则
     */
    private boolean isCookieBuuidValid(Cookie cookie) {
        return cookie != null &&
               cookie.getInfo() != null &&
               cookie.getInfo().getBuuid() != null;
    }

    /**
     * 确定算法请求URL
     *
     * <p>根据算法类型选择合适的算法服务URL。UGC算法类型使用专门的UGC算法URL，
     * 其他算法类型使用通用的推荐算法URL。</p>
     *
     * <h3>URL选择逻辑：</h3>
     * <ul>
     *   <li>UGC算法类型：使用UGC专用算法URL</li>
     *   <li>其他算法类型：使用通用推荐算法URL</li>
     * </ul>
     *
     * @param algorithmType 算法类型
     * @return 对应的算法服务URL
     */
    private String determineAlgorithmUrl(Integer algorithmType) {
        if (longVideoConfig.getAlgorithmTypeUGC().equals(algorithmType)) {
            return longVideoTrailerRecommendConfig.getUgcAlgorithmUrl();
        }
        return longVideoTrailerRecommendConfig.getRecommendAlgorithmUrl();
    }

    /**
     * 添加版本特定参数
     *
     * <p>根据客户端版本和算法类型决定是否添加源列表参数。
     * 只有特定的算法类型和有版本信息的请求才会添加源列表。</p>
     *
     * <h3>业务逻辑：</h3>
     * <ul>
     *   <li>根据客户端版本计算适配的合作方列表</li>
     *   <li>算法侧只返回r_source_list内的节目</li>
     *   <li>仅对算法类型一和类型二生效</li>
     * </ul>
     *
     * @param params 参数Map，用于添加版本特定参数
     * @param reqProperty 请求属性对象，包含版本信息
     * @param algorithmType 算法类型
     */
    private void addVersionSpecificParams(Map<String, String> params, FeedsListReqProperty reqProperty, Integer algorithmType) {
        if (shouldAddSourceList(reqProperty, algorithmType)) {
            params.put("r_source_list", sourceVersionService.getSourceStrByVersion(reqProperty.getVersion()));
        }
    }

    /**
     * 判断是否应该添加源列表参数
     *
     * <p>只有当请求包含版本信息且算法类型为类型一或类型二时，才需要添加源列表参数。
     * 该方法将复杂的条件判断提取为独立方法。</p>
     *
     * <h3>添加条件：</h3>
     * <ul>
     *   <li>请求包含版本信息（version不为null）</li>
     *   <li>算法类型为类型一或类型二</li>
     * </ul>
     *
     * @param reqProperty 请求属性对象
     * @param algorithmType 算法类型
     * @return true 如果应该添加源列表，false 否则
     */
    private boolean shouldAddSourceList(FeedsListReqProperty reqProperty, Integer algorithmType) {
        return reqProperty.getVersion() != null &&
               (longVideoConfig.getAlgorithmTypeOne().equals(algorithmType) ||
                longVideoConfig.getAlgorithmTypeTwo().equals(algorithmType));
    }

    /**
     * 执行算法请求
     *
     * <p>发送HTTP请求到算法服务并处理响应。如果请求发生异常，则返回缓存的降级响应。
     * 该方法将HTTP请求执行和异常处理逻辑独立出来。</p>
     *
     * <h3>处理流程：</h3>
     * <ol>
     *   <li>发送异步HTTP请求到算法服务</li>
     *   <li>如果请求异常，返回降级缓存响应</li>
     *   <li>使用handle方法处理正常响应和异常情况</li>
     * </ol>
     *
     * <h3>异常处理：</h3>
     * <ul>
     *   <li>HTTP请求异常：返回缓存降级响应</li>
     *   <li>响应处理异常：在handle方法中统一处理</li>
     * </ul>
     *
     * @param algorithmUrl 算法服务URL
     * @param params 请求参数Map
     * @param reqProperty 请求属性对象，用于降级处理
     * @param algorithmType 算法类型，用于降级处理
     * @return CompletableFuture包装的算法响应
     */
    private CompletableFuture<LvtRecommendAlgorithmResponse> executeAlgorithmRequest(
            String algorithmUrl, Map<String, String> params, FeedsListReqProperty reqProperty, Integer algorithmType) {

        CompletableFuture<LvtRecommendAlgorithmResponse> future;
        try {
            future = httpDataChannel.asyncGetForObject(algorithmUrl, LvtRecommendAlgorithmResponse.class,
                    params, longVideoTrailerRecommendConfig.getRecommendAlgorithmTimeout());
        } catch (Exception e) {
            log.error("get lvtRecommendAlgorithmResponse error, the params:{}, the exception:{}", params, e);
            return CompletableFuture.completedFuture(
                    algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, algorithmType, FALLBACK_TYPE_HTTP_ERROR));
        }

        return future.handle((response, e) -> handleAlgorithmResponse(response, e, params, reqProperty, algorithmType));
    }

    /**
     * 处理算法响应
     *
     * <p>处理算法服务返回的响应，包括异常情况和空数据的处理。
     * 如果响应无效或数据为空，则返回缓存的降级响应。</p>
     *
     * <h3>响应验证：</h3>
     * <ul>
     *   <li>检查是否有异常发生</li>
     *   <li>检查响应对象是否为null</li>
     *   <li>检查响应状态是否为成功（0）</li>
     *   <li>检查响应数据是否为空</li>
     * </ul>
     *
     * <h3>降级策略：</h3>
     * <ul>
     *   <li>响应无效时返回缓存降级响应</li>
     *   <li>数据为空时返回缓存降级响应</li>
     *   <li>正常情况下返回原始响应</li>
     * </ul>
     *
     * @param response 算法服务响应
     * @param e 可能发生的异常
     * @param params 请求参数，用于日志记录
     * @param reqProperty 请求属性对象，用于降级处理
     * @param algorithmType 算法类型，用于降级处理
     * @return 处理后的算法响应
     */
    private LvtRecommendAlgorithmResponse handleAlgorithmResponse(
            LvtRecommendAlgorithmResponse response, Throwable e, Map<String, String> params,
            FeedsListReqProperty reqProperty, Integer algorithmType) {

        if (isResponseInvalid(response, e)) {
            log.error("get lvtRecommendAlgorithmResponse error, the params:{}, the response:{}", params, response, e);
            return algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, algorithmType, FALLBACK_TYPE_HTTP_ERROR);
        }

        if (CollectionUtils.isEmpty(response.getData())) {
            log.error("getRecommendAlgorithmSids return null, the params:{}", params);
            return algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, algorithmType, FALLBACK_TYPE_HTTP_ERROR);
        }

        return response;
    }

    /**
     * 判断响应是否无效
     *
     * <p>检查算法服务响应是否有效。响应无效的情况包括：异常发生、响应为null、
     * 状态为null或状态不为0（成功）。该方法将复杂的 || 条件判断提取为独立方法。</p>
     *
     * <h3>无效响应条件：</h3>
     * <ul>
     *   <li>异常不为null（请求或处理过程中发生异常）</li>
     *   <li>响应对象为null</li>
     *   <li>响应状态为null</li>
     *   <li>响应状态不为0（非成功状态）</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 || 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @param response 算法服务响应
     * @param e 可能发生的异常
     * @return true 如果响应无效，false 如果响应有效
     */
    private boolean isResponseInvalid(LvtRecommendAlgorithmResponse response, Throwable e) {
        return e != null ||
               response == null ||
               response.getStatus() == null ||
               response.getStatus() != 0;
    }

    /**
     * 获取剧头
     *
     * @param sidSet
     * @return
     */
    public CompletableFuture<RpcResult<Map<String/* sid */, StandardAlbum>>> getAlbumsBySids(Set<String> sidSet) {
        return albumRpcApi.getBySidsFilterInvalid(sidSet.stream().collect(Collectors.toList()));
    }

    public CompletableFuture<RpcResult<Map<String/*vid*/, StandardVideo>>> getVideosByVids(Map<String, String> vidSidMap) {
        return videoRpcApi.getByVids(vidSidMap);
    }


    /**
     * 批量查询虚拟sid
     *
     * @param sidSet
     * @return
     */
    public CompletableFuture<RpcResult<Map<String/* sid */, MisVirtualProgramRelation>>> queryVirtualSidsBySids(Set<String> sidSet) {
        return virtualProgramRelationRpcApi.queryBySids(sidSet.stream().collect(Collectors.toList()));
    }


}