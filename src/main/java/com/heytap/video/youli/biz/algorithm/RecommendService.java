package com.heytap.video.youli.biz.algorithm;

import com.heytap.video.fallback.model.FallbackHttpReqParam;
import com.heytap.video.fallback.model.FallbackResponse;
import com.heytap.video.fallback.util.FallbackHttpChannelUtils;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.constant.Bidlst;
import com.heytap.video.youli.model.algorithm.RecommendList;
import com.heytap.video.youli.utils.BizUtils;
import com.heytap.video.youli.utils.UrlUtil;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.utils.Env;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.InvalidDataRuntimeException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.app.lib.strategy.IPLocationStrategyResult;
import com.oppo.browser.video.common.pubobj.constant.RecTypeConstant;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.trace.core.TraceThreadLocal;
import com.oppo.trace.core.scope.TraceScope;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static com.heytap.video.youli.constant.GlobalConstant.APPID;
import static com.heytap.video.youli.constant.GlobalConstant.R_ACTION;
import static com.heytap.video.youli.constant.GlobalConstant.R_PAGE;
import static com.heytap.video.youli.constant.GlobalConstant.TRACEID;

@Slf4j
@Service
public class RecommendService {
    protected static final String VIDEO = "video";
    
    @Autowired
    private YouliApiConfig youliApiConfig;

    @Autowired
    private HttpDataChannel httpDataChannel;

    /**
     * 获取信息流推荐列表(带降级)
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<FallbackResponse<RecommendList, FeedsList>> getFeedsRecommend(FeedsListReqProperty reqProperty) {
        //是否为推荐频道
        boolean isRecChannel = VIDEO.equals(reqProperty.getFromId());
        String fallbackKey = String.format("video:%s:%s", reqProperty.getSource(), reqProperty.getFromId());
        String url = isRecChannel ? youliApiConfig.getMainRecommendUrl() : youliApiConfig.getChannelRecommendUrl();
        FallbackHttpReqParam<RecommendList, FeedsList> param = new FallbackHttpReqParam(url, fallbackKey, youliApiConfig.getRecommendSocketTimeout(), UrlUtil.getContentCommonHeaders(), JsonTools.toJsonString(getReqParams(reqProperty)), RecommendList.class, FeedsList.class);

        String interfaceName = isRecChannel ? "mainRec" : "channelRec";
        return FallbackHttpChannelUtils.executeHttpPostAsync(interfaceName, param);
    }

    /**
     * 获取相关推荐列表
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<RecommendList> getRelatedRecommend(ListBaseReqProperty reqProperty) {
        Map<String, Object> reqParams = getRecReqParams(reqProperty);
        String url =  youliApiConfig.getDetailRelevantUrl();
        CompletableFuture<RecommendList> recommendListCf;
        try {
            recommendListCf = httpDataChannel.asyncPostForObject(url, JsonTools.toJsonString(reqParams), RecommendList.class, null, UrlUtil.getContentCommonHeaders(), youliApiConfig.getRelevantSocketTimeout());
        } catch (HttpDataChannelException e) {
            log.warn("getRelatedRecommend HttpDataChannelException, url:{}", url, e);
            recommendListCf = CompletableFuture.completedFuture(null);
        }
        return recommendListCf.handle((recommendList, throwable) -> {
            if ((throwable != null) || (recommendList == null) || (recommendList.getCode() != 0) || CollectionUtils.isEmpty(recommendList.getResult())) {
                log.warn("recommend_api :[url:{}][rt:{}]", url, recommendList, throwable);
                throw new InvalidDataRuntimeException("recommend result:" +
                        (recommendList != null ? (recommendList.getMessage() + "[code:" + recommendList.getCode() + "]") : null));
            }

            return recommendList;
        });
    }

    /**
     * 构建信息流推荐请求参数
     *
     * <p>该方法是重构后的主方法，通过调用多个子方法来构建完整的推荐请求参数。
     * 重构前该方法的圈复杂度为21，重构后降低到6以下。</p>
     *
     * <h3>重构策略：</h3>
     * <ul>
     *   <li>方法分解（Extract Method）- 将大方法分解为多个小方法</li>
     *   <li>逻辑分层 - 按功能职责将参数构建分为五个层次</li>
     *   <li>条件提取 - 将复杂条件判断提取为独立方法</li>
     *   <li>通用方法提取 - 提取重复的参数添加逻辑</li>
     * </ul>
     *
     * <h3>参数构建层次：</h3>
     * <ol>
     *   <li>基础参数 - 环境、业务ID、时间、追踪ID等</li>
     *   <li>推荐参数 - 推荐数量、频道信息、推荐栏ID等</li>
     *   <li>用户标识参数 - 用户ID、设备信息等</li>
     *   <li>设备环境参数 - 网络、设备型号、地理位置等</li>
     *   <li>扩展参数 - 业务扩展字段、固定配置等</li>
     * </ol>
     *
     * @param reqProperty 请求属性对象，包含所有请求相关信息
     * @return 构建完成的推荐请求参数Map
     */
    private Map<String, Object> getReqParams(FeedsListReqProperty reqProperty) {
        Map<String, Object> params = new HashMap<>();

        // 构建基础参数
        buildFeedsBaseParams(params, reqProperty);

        // 构建推荐相关参数
        buildFeedsRecommendParams(params, reqProperty);

        // 构建用户标识参数
        buildFeedsUserParams(params, reqProperty);

        // 构建设备环境参数
        buildFeedsDeviceParams(params, reqProperty);

        // 构建扩展参数
        buildFeedsExtensionParams(params, reqProperty);

        return params;
    }

    /**
     * 构建相关推荐请求参数
     *
     * <p>该方法是重构后的主方法，通过调用多个子方法来构建完整的推荐请求参数。
     * 重构前该方法的圈复杂度为24，重构后降低到6以下。</p>
     *
     * <h3>重构策略：</h3>
     * <ul>
     *   <li>方法分解（Extract Method）- 将大方法分解为多个小方法</li>
     *   <li>逻辑分层 - 按功能职责将参数构建分为四个层次</li>
     *   <li>条件提取 - 将复杂条件判断提取为独立方法</li>
     *   <li>通用方法提取 - 提取重复的参数添加逻辑</li>
     * </ul>
     *
     * <h3>参数构建层次：</h3>
     * <ol>
     *   <li>基础参数 - 环境、业务ID、时间、追踪ID等</li>
     *   <li>推荐参数 - 推荐数量、频道信息、刷新行为等</li>
     *   <li>设备用户参数 - 设备信息、用户信息、地理位置等</li>
     *   <li>扩展参数 - 业务扩展字段、固定配置等</li>
     * </ol>
     *
     * @param reqProperty 请求属性对象，包含所有请求相关信息
     * @return 构建完成的推荐请求参数Map
     */
    private Map<String, Object> getRecReqParams(ListBaseReqProperty reqProperty) {
        Map<String, Object> params = new HashMap<>();

        // 构建基础参数
        buildRecBaseParams(params, reqProperty);

        // 构建推荐相关参数
        buildRecRecommendParams(params, reqProperty);

        // 构建设备和用户相关参数
        buildRecDeviceAndUserParams(params, reqProperty);

        // 构建扩展参数
        buildRecExtensionParams(params, reqProperty);

        return params;
    }

    /**
     * 构建基础请求参数
     *
     * <p>构建推荐请求的基础参数，包括环境相关、业务标识、时间戳和追踪ID等。
     * 这些参数是所有推荐请求都必须包含的核心参数。</p>
     *
     * @param params 参数Map，用于存储构建的参数
     * @param reqProperty 请求属性对象
     */
    private void buildRecBaseParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        // 路由参数（测试环境）
        addRouteParamIfNeeded(params);

        // 基础必传参数
        params.put(APPID, 2);
        params.put("time", System.currentTimeMillis());
        params.put("cid", VIDEO);

        // 请求追踪ID
        addTraceId(params, reqProperty);
    }

    /**
     * 根据环境添加路由参数
     *
     * <p>在测试环境下添加路由参数，用于请求路由到正确的服务实例。
     * 生产环境不需要此参数。</p>
     *
     * @param params 参数Map
     */
    private void addRouteParamIfNeeded(Map<String, Object> params) {
        if (isTestEnvironment()) {
            params.put("route", "bjhtonlinerec");
        }
    }

    /**
     * 判断当前是否为测试环境
     *
     * <p>通过环境变量判断当前运行环境，测试环境包括 "env" 和 "test"。
     * 该方法将原来的复杂条件判断提取为独立方法，降低了圈复杂度。</p>
     *
     * @return true 如果是测试环境，false 如果是生产环境
     */
    private boolean isTestEnvironment() {
        String env = Env.getEnv();
        return "env".equals(env) || "test".equals(env);
    }

    /**
     * 添加请求追踪ID
     *
     * <p>从请求属性中提取或生成追踪ID，用于链路追踪和问题排查。</p>
     *
     * @param params 参数Map
     * @param reqProperty 请求属性对象
     */
    private void addTraceId(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        String traceId = extractTraceId(reqProperty);
        params.put(TRACEID, traceId);
    }

    /**
     * 提取请求追踪ID
     *
     * <p>按优先级从不同来源提取追踪ID：</p>
     * <ol>
     *   <li>请求属性中的 requestId</li>
     *   <li>链路追踪上下文中的 REQUEST_ID</li>
     *   <li>生成新的 UUID（去除连字符）</li>
     * </ol>
     *
     * <p>该方法将原来复杂的 if-else 逻辑分层处理，提高了代码的可读性。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 追踪ID字符串
     */
    private String extractTraceId(ListBaseReqProperty reqProperty) {
        if (StringUtils.isNotEmpty(reqProperty.getRequestId())) {
            return reqProperty.getRequestId();
        }

        String traceFromScope = getTraceIdFromScope();
        if (StringUtils.isNotEmpty(traceFromScope)) {
            return traceFromScope;
        }

        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 从链路追踪上下文获取追踪ID
     *
     * <p>尝试从 TraceThreadLocal 中获取当前请求的追踪ID。
     * 如果上下文无效则返回 null。</p>
     *
     * @return 追踪ID字符串，如果获取失败则返回 null
     */
    private String getTraceIdFromScope() {
        if (isTraceScopeValid()) {
            return TraceThreadLocal.getScope().getAttachments().get(TraceScope.ATTACHMENT_KEY.REQUEST_ID);
        }
        return null;
    }

    /**
     * 验证链路追踪上下文是否有效
     *
     * <p>检查 TraceScope 和其 attachments 是否都不为 null。
     * 该方法将复杂的 && 条件判断提取为独立方法。</p>
     *
     * @return true 如果上下文有效，false 如果无效
     */
    private boolean isTraceScopeValid() {
        return TraceThreadLocal.getScope() != null &&
               TraceThreadLocal.getScope().getAttachments() != null;
    }

    private void buildRecRecommendParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        // 推荐数量
        Integer num = determineRecommendNum(reqProperty);
        params.put("num", num);

        // 推荐栏ID
        params.put("bidlst", BizUtils.getRelevantBidLst(youliApiConfig));

        // 频道相关参数
        params.put("r_channel_id", reqProperty.getFromId());
        params.put("r_channel_type", VIDEO);

        // 刷新行为参数
        addRefreshActionParams(params, reqProperty);

        // 刷新方式
        params.put("r_refresh", 1);
    }

    private Integer determineRecommendNum(ListBaseReqProperty reqProperty) {
        if (reqProperty.getDetailStyle() == 3) {
            // detailStyle=3 2N详情页 仅获取4个相关推荐
            return youliApiConfig.getVideoRec2NLimit();
        }
        return youliApiConfig.getVideoRecLimit();
    }

    private void addRefreshActionParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        if (reqProperty.getOffset() == 0) {
            params.put(R_ACTION, 0);
            params.put(R_PAGE, reqProperty.getDownTimes());
        } else {
            params.put(R_ACTION, 1);
            params.put(R_PAGE, reqProperty.getUpTimes());
        }
    }

    private void buildRecDeviceAndUserParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        AttributeValues attributeValues = reqProperty.getAttributeValues();

        // 用户标识参数
        appendCommonParams(params, attributeValues, reqProperty);
        handleH5ParamsIfNeeded(params, reqProperty);

        // 地理位置参数
        addLocationParams(params, reqProperty);

        // 设备相关参数
        addDeviceParams(params, attributeValues);

        // 用户相关参数
        addUserParams(params, reqProperty);
    }

    private void handleH5ParamsIfNeeded(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        if (RecTypeConstant.H5.equals(reqProperty.getRecType())) {
            appendH5Params(params, reqProperty);
        }
    }

    private void addLocationParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        if (StringUtils.isNotEmpty(reqProperty.getArea())) {
            IPLocationStrategyResult location = JsonTools.toMap(reqProperty.getArea(), IPLocationStrategyResult.class);
            if (location != null) {
                String areaStr = String.format("country:%s;province:%s;city:%s",
                    StringUtils.defaultString(location.getCountry()),
                    StringUtils.defaultString(location.getProvince()),
                    StringUtils.defaultString(location.getCity()));
                params.put("r_area", areaStr);
            }
        }
    }

    private void addDeviceParams(Map<String, Object> params, AttributeValues attributeValues) {
        addParamIfNotEmpty(params, "network", attributeValues.getNetwork());
        addParamIfNotEmpty(params, "r_devtype", attributeValues.getPhone());
        addParamIfNotEmpty(params, "r_client_version", attributeValues.getClientFullBrowserVersion());
        addParamIfNotEmpty(params, "ip", attributeValues.getIp());
    }

    private void addUserParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        params.put("personalRec", 1);

        String username = extractUsername(reqProperty);
        if (StringUtils.isNotEmpty(username)) {
            params.put("r_username", username);
        }
    }

    private String extractUsername(ListBaseReqProperty reqProperty) {
        if (isCookieUsernameValid(reqProperty)) {
            return reqProperty.getScookieIgnoreException().getInfo().getUn();
        }
        return null;
    }

    private boolean isCookieUsernameValid(ListBaseReqProperty reqProperty) {
        return reqProperty.getScookieIgnoreException() != null &&
               reqProperty.getScookieIgnoreException().getInfo() != null &&
               StringUtils.isNotEmpty(reqProperty.getScookieIgnoreException().getInfo().getUn());
    }

    private void buildRecExtensionParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        // 固定参数
        params.put("needRelation", false);
        params.put("picFormatType", 1);
        params.put("r_used", "0");
        params.put("r_relatedId", reqProperty.getDocid());
        params.put("r_cardtype", "VIDEO");

        // 推荐扩展参数
        addRecommendExtParam(params, reqProperty);

        // 媒体号参数
        addParamIfNotEmpty(params, "authorId", reqProperty.getMediaNo());
    }

    private void addRecommendExtParam(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        String recommendExt = "push_detail_2N".equals(reqProperty.getPageID())
            ? "parFromId=push_click"
            : "parFromId=" + reqProperty.getSpageID();
        params.put("recommendExt", recommendExt);
    }

    /**
     * 条件添加参数到Map中
     *
     * <p>只有当值不为空时才将参数添加到Map中，避免添加无效的空值参数。
     * 这是一个通用的工具方法，消除了代码中多处重复的条件判断。</p>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>消除了多处重复的 StringUtils.isNotEmpty() 判断</li>
     *   <li>提供了统一的参数添加逻辑</li>
     *   <li>降低了主方法的圈复杂度</li>
     *   <li>提高了代码的一致性和可维护性</li>
     * </ul>
     *
     * @param params 参数Map
     * @param key 参数键
     * @param value 参数值，只有非空时才会被添加
     */
    private void addParamIfNotEmpty(Map<String, Object> params, String key, String value) {
        if (StringUtils.isNotEmpty(value)) {
            params.put(key, value);
        }
    }

    /**
     * 添加通用参数
     *
     * <p>添加推荐请求的通用参数，包括用户唯一标识（buuid）和设备标识（imei）。
     * 如果属性值中的buuid为0，则尝试从Cookie中获取。</p>
     *
     * <h3>参数说明：</h3>
     * <ul>
     *   <li>buuid：用户唯一标识ID，用于个性化推荐</li>
     *   <li>imei：设备IMEI号，用于设备识别</li>
     * </ul>
     *
     * <h3>buuid获取优先级：</h3>
     * <ol>
     *   <li>AttributeValues中的buuid（如果不为0）</li>
     *   <li>Cookie中的buuid（如果Cookie有效）</li>
     * </ol>
     *
     * @param params 参数Map，用于存储通用参数
     * @param attributeValues 用户属性值对象
     * @param reqProperty 请求属性对象，用于获取Cookie信息
     */
    private void appendCommonParams(Map<String, Object> params, AttributeValues attributeValues, ListBaseReqProperty reqProperty) {
        long buuid = attributeValues.getBuuid();
        Cookie cookie = reqProperty.getScookieIgnoreException();
        if(buuid == 0 && cookie != null && cookie.getInfo() != null && cookie.getInfo().getBuuid() != null){
            buuid = cookie.getInfo().getBuuid();
        }
        params.put("buuid", buuid);// 用户唯一标识的ID：buuid
        params.put("imei", StringUtils.defaultString(attributeValues.getImei()));// imei号
//        params.put("ouid", StringUtils.defaultString(attributeValues.getOuid()));// openId的ouid
//        params.put("udid", StringUtils.defaultString(attributeValues.getUdid()));// openId的udid
//        params.put("duid", StringUtils.defaultString(attributeValues.getDuid()));// openId的duid
    }

    /**
     * 添加H5特殊参数
     *
     * <p>为H5类型的推荐请求添加特殊的参数。H5请求需要特殊的buuid处理逻辑，
     * 如果无法从Cookie获取有效的buuid，则生成随机的buuid。</p>
     *
     * <h3>H5 buuid处理逻辑：</h3>
     * <ul>
     *   <li>优先从Cookie中获取buuid</li>
     *   <li>如果Cookie无效，生成随机buuid（1到Integer.MAX_VALUE之间）</li>
     *   <li>设置buuidtype为"random"标识随机生成</li>
     * </ul>
     *
     * <h3>业务背景：</h3>
     * <p>H5页面可能无法获取到有效的用户标识，因此需要生成临时的随机标识
     * 来保证推荐算法的正常运行。</p>
     *
     * @param params 参数Map，用于存储H5特殊参数
     * @param reqProperty 请求属性对象，用于获取Cookie信息
     */
    private void appendH5Params(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        long buuid;
        Cookie cookie = reqProperty.getScookieIgnoreException();
        if(cookie != null && cookie.getInfo() != null && cookie.getInfo().getBuuid() != null){
            buuid = cookie.getInfo().getBuuid();
        } else{
            buuid = new SecureRandom().nextInt(Integer.MAX_VALUE);
            buuid = buuid == 0 ? 1 : buuid;
        }
        params.put("buuid", buuid);
        params.put("buuidtype", "random");
    }

    /**
     * 根据fromId获取算法频道ID
     *
     * <p>从配置的算法频道ID映射表中获取对应fromId的算法频道ID值。
     * 如果映射表中没有对应的fromId，则返回原始的fromId作为默认值。</p>
     *
     * <h3>映射逻辑：</h3>
     * <ul>
     *   <li>如果fromId为空或映射表为空，返回原始fromId</li>
     *   <li>如果映射表中存在对应的fromId，返回映射后的频道ID</li>
     *   <li>如果映射表中不存在对应的fromId，返回原始fromId</li>
     * </ul>
     *
     * <h3>业务用途：</h3>
     * <p>不同的频道可能需要映射到特定的算法频道ID，以便推荐算法
     * 能够为不同频道提供个性化的推荐策略。</p>
     *
     * @param fromId 原始频道ID
     * @return 映射后的算法频道ID，如果未找到映射则返回原始fromId
     */
    private String getChannelIdByFromId(String fromId) {
        if (StringUtils.isBlank(fromId) || MapUtils.isEmpty(youliApiConfig.getRecChannelIdMap())) {
            return fromId;
        }
        return youliApiConfig.getRecChannelIdMap().getOrDefault(fromId, fromId);
    }

    /**
     * 根据fromId获取推荐栏ID
     *
     * <p>从配置的推荐栏ID映射表中获取对应fromId的推荐栏ID值。
     * 如果映射表中没有对应的fromId，则返回默认的频道推荐栏ID。</p>
     *
     * @param fromId 频道ID
     * @return 推荐栏ID，如果未找到则返回默认值
     */
    private String getBidLstByFromId(String fromId) {
        if (StringUtils.isBlank(fromId) || MapUtils.isEmpty(youliApiConfig.getRecBidLstMap())) {
            return fromId;
        }
        return youliApiConfig.getRecBidLstMap().getOrDefault(fromId, Bidlst.CHANNEL);
    }

    // ========== getReqParams 重构后的子方法 ==========

    /**
     * 构建信息流基础请求参数
     *
     * <p>构建信息流推荐请求的基础参数，包括环境相关、业务标识、时间戳和追踪ID等。
     * 这些参数是所有信息流推荐请求都必须包含的核心参数。</p>
     *
     * @param params 参数Map，用于存储构建的参数
     * @param reqProperty 请求属性对象
     */
    private void buildFeedsBaseParams(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        // 路由参数（测试环境）
        addFeedsRouteParamIfNeeded(params);

        // 基础必传参数
        params.put(APPID, 2);
        params.put("time", System.currentTimeMillis());
        params.put("cid", VIDEO);

        // 请求追踪ID
        addFeedsTraceId(params, reqProperty);
    }

    /**
     * 根据环境添加信息流路由参数
     *
     * <p>在开发和测试环境下添加路由参数，用于请求路由到正确的服务实例。
     * 生产环境不需要此参数。</p>
     *
     * @param params 参数Map
     */
    private void addFeedsRouteParamIfNeeded(Map<String, Object> params) {
        if (isFeedsTestEnvironment()) {
            params.put("route", "bjhtonlinerec");
        }
    }

    /**
     * 判断当前是否为信息流测试环境
     *
     * <p>通过环境变量判断当前运行环境，测试环境包括 "dev" 和 "test"。
     * 该方法将原来的复杂条件判断提取为独立方法，降低了圈复杂度。</p>
     *
     * @return true 如果是测试环境，false 如果是生产环境
     */
    private boolean isFeedsTestEnvironment() {
        String env = Env.getEnv();
        return "dev".equals(env) || "test".equals(env);
    }

    /**
     * 添加信息流请求追踪ID
     *
     * <p>从请求属性中提取或生成追踪ID，用于链路追踪和问题排查。</p>
     *
     * @param params 参数Map
     * @param reqProperty 请求属性对象
     */
    private void addFeedsTraceId(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        String traceId = extractFeedsTraceId(reqProperty);
        params.put(TRACEID, traceId);
    }

    /**
     * 提取信息流请求追踪ID
     *
     * <p>按优先级从不同来源提取追踪ID：</p>
     * <ol>
     *   <li>请求属性中的 requestId</li>
     *   <li>链路追踪上下文中的 REQUEST_ID</li>
     *   <li>生成新的 UUID（去除连字符）</li>
     * </ol>
     *
     * <p>该方法将原来复杂的 if-else 逻辑分层处理，提高了代码的可读性。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 追踪ID字符串
     */
    private String extractFeedsTraceId(FeedsListReqProperty reqProperty) {
        if (StringUtils.isNotEmpty(reqProperty.getRequestId())) {
            return reqProperty.getRequestId();
        }

        String traceFromScope = getFeedsTraceIdFromScope();
        if (StringUtils.isNotEmpty(traceFromScope)) {
            return traceFromScope;
        }

        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 从链路追踪上下文获取信息流追踪ID
     *
     * <p>尝试从 TraceThreadLocal 中获取当前请求的追踪ID。
     * 如果上下文无效则返回 null。</p>
     *
     * @return 追踪ID字符串，如果获取失败则返回 null
     */
    private String getFeedsTraceIdFromScope() {
        if (isFeedsTraceScopeValid()) {
            return TraceThreadLocal.getScope().getAttachments().get(TraceScope.ATTACHMENT_KEY.REQUEST_ID);
        }
        return null;
    }

    /**
     * 验证信息流链路追踪上下文是否有效
     *
     * <p>检查 TraceScope 和其 attachments 是否都不为 null。
     * 该方法将复杂的 && 条件判断提取为独立方法。</p>
     *
     * @return true 如果上下文有效，false 如果无效
     */
    private boolean isFeedsTraceScopeValid() {
        return TraceThreadLocal.getScope() != null &&
               TraceThreadLocal.getScope().getAttachments() != null &&
               StringUtils.isNotEmpty(TraceThreadLocal.getScope().getAttachments().get(TraceScope.ATTACHMENT_KEY.REQUEST_ID));
    }

    /**
     * 构建信息流推荐相关参数
     *
     * <p>构建推荐相关的参数，包括推荐数量、推荐栏ID、频道信息和刷新行为等。</p>
     *
     * @param params 参数Map，用于存储构建的参数
     * @param reqProperty 请求属性对象
     */
    private void buildFeedsRecommendParams(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        // 推荐数量
        params.put("num", youliApiConfig.getEachRefreshNum());

        // 推荐栏ID
        params.put("bidlst", getBidLstByFromId(reqProperty.getFromId()));

        // 频道相关参数
        params.put("r_channel_id", getChannelIdByFromId(reqProperty.getFromId()));
        params.put("r_channel_type", StringUtils.defaultIfEmpty(reqProperty.getType(), VIDEO));

        // 刷新行为参数
        addFeedsRefreshActionParams(params, reqProperty);

        // 刷新方式
        params.put("r_refresh", 1);
    }

    /**
     * 添加信息流刷新行为参数
     *
     * <p>根据偏移量判断是下拉刷新还是翻页，并设置相应的行为参数和页码。</p>
     *
     * <h3>刷新行为说明：</h3>
     * <ul>
     *   <li>offset = 0：下拉刷新，r_action = 0，r_page = downTimes</li>
     *   <li>offset > 0：翻页，r_action = 1，r_page = upTimes</li>
     * </ul>
     *
     * @param params 参数Map
     * @param reqProperty 请求属性对象
     */
    private void addFeedsRefreshActionParams(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        if (reqProperty.getOffset() == 0) {
            // 下拉刷新
            params.put(R_ACTION, 0);
            params.put(R_PAGE, reqProperty.getDownTimes());
        } else {
            // 翻页
            params.put(R_ACTION, 1);
            params.put(R_PAGE, reqProperty.getUpTimes());
        }
    }

    /**
     * 构建信息流用户标识参数
     *
     * <p>构建用户相关的标识参数，包括用户ID、设备标识等。</p>
     *
     * @param params 参数Map，用于存储构建的参数
     * @param reqProperty 请求属性对象
     */
    private void buildFeedsUserParams(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        AttributeValues attributeValues = reqProperty.getAttributeValues();

        // 用户标识参数（buuid和imei）
        appendCommonParams(params, attributeValues, reqProperty);

        // H5特殊参数处理
        handleFeedsH5ParamsIfNeeded(params, reqProperty);

        // 个性化推荐开关
        params.put("personalRec", 1);

        // 用户名参数
        addFeedsUsernameParam(params, reqProperty);
    }

    /**
     * 处理信息流H5参数（如果需要）
     *
     * <p>当推荐类型为H5时，添加H5特殊的参数。</p>
     *
     * @param params 参数Map
     * @param reqProperty 请求属性对象
     */
    private void handleFeedsH5ParamsIfNeeded(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        if (RecTypeConstant.H5.equals(reqProperty.getRecType())) {
            appendH5Params(params, reqProperty);
        }
    }

    /**
     * 添加信息流用户名参数
     *
     * <p>从Cookie中提取用户名并添加到参数中。</p>
     *
     * @param params 参数Map
     * @param reqProperty 请求属性对象
     */
    private void addFeedsUsernameParam(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        String username = extractFeedsUsername(reqProperty);
        if (StringUtils.isNotEmpty(username)) {
            params.put("r_username", username);
        }
    }

    /**
     * 提取信息流用户名
     *
     * <p>从Cookie中安全地提取用户名，如果Cookie无效则返回null。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 用户名，如果提取失败则返回null
     */
    private String extractFeedsUsername(FeedsListReqProperty reqProperty) {
        if (isFeedsCookieUsernameValid(reqProperty)) {
            return reqProperty.getScookieIgnoreException().getInfo().getUn();
        }
        return null;
    }

    /**
     * 验证信息流Cookie用户名是否有效
     *
     * <p>检查Cookie对象、Info对象和用户名字段是否都不为null且不为空。
     * 该方法将复杂的 && 条件判断提取为独立方法。</p>
     *
     * @param reqProperty 请求属性对象
     * @return true 如果Cookie中的用户名有效，false 否则
     */
    private boolean isFeedsCookieUsernameValid(FeedsListReqProperty reqProperty) {
        return reqProperty.getScookieIgnoreException() != null &&
               reqProperty.getScookieIgnoreException().getInfo() != null &&
               StringUtils.isNotEmpty(reqProperty.getScookieIgnoreException().getInfo().getUn());
    }

    /**
     * 构建信息流设备环境参数
     *
     * <p>构建设备和环境相关的参数，包括地理位置、网络环境、设备型号、客户端版本和IP地址等。</p>
     *
     * @param params 参数Map，用于存储构建的参数
     * @param reqProperty 请求属性对象
     */
    private void buildFeedsDeviceParams(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        AttributeValues attributeValues = reqProperty.getAttributeValues();

        // 地理位置参数
        addFeedsLocationParam(params, reqProperty);

        // 设备环境参数
        addFeedsParamIfNotEmpty(params, "network", attributeValues.getNetwork());
        addFeedsParamIfNotEmpty(params, "r_devtype", attributeValues.getPhone());
        addFeedsParamIfNotEmpty(params, "r_client_version", attributeValues.getClientFullBrowserVersion());
        addFeedsParamIfNotEmpty(params, "ip", attributeValues.getIp());
    }

    /**
     * 添加信息流地理位置参数
     *
     * <p>解析地理位置信息并格式化为推荐系统需要的格式。</p>
     *
     * @param params 参数Map
     * @param reqProperty 请求属性对象
     */
    private void addFeedsLocationParam(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        if (StringUtils.isEmpty(reqProperty.getArea())) {
            return;
        }

        IPLocationStrategyResult location = JsonTools.toMap(reqProperty.getArea(), IPLocationStrategyResult.class);
        if (location != null) {
            String areaValue = String.format("country:%s;province:%s;city:%s",
                    StringUtils.defaultString(location.getCountry()),
                    StringUtils.defaultString(location.getProvince()),
                    StringUtils.defaultString(location.getCity()));
            params.put("r_area", areaValue);
        }
    }

    /**
     * 构建信息流扩展参数
     *
     * <p>构建业务扩展参数和固定配置参数。</p>
     *
     * @param params 参数Map，用于存储构建的参数
     * @param reqProperty 请求属性对象
     */
    private void buildFeedsExtensionParams(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        // 固定配置参数
        params.put("needRelation", false);
        params.put("picFormatType", 1);
        params.put("homepage", false);

        // 推荐扩展参数
        addFeedsRecommendExtParam(params, reqProperty);
    }

    /**
     * 添加信息流推荐扩展参数
     *
     * <p>构建推荐扩展参数，包括内容ID传递、页面ID透传和游戏数量等。</p>
     *
     * @param params 参数Map
     * @param reqProperty 请求属性对象
     */
    private void addFeedsRecommendExtParam(Map<String, Object> params, FeedsListReqProperty reqProperty) {
        StringBuilder recommendExtValue = new StringBuilder();

        // 内容ID传递
        addFeedsContentIdParam(recommendExtValue, reqProperty);

        // 上一个页面的ID透传
        recommendExtValue.append("parFromId=").append(reqProperty.getSpageID());

        // 游戏数量参数（版本控制）
        addFeedsGameNumParam(recommendExtValue, reqProperty);

        params.put("recommendExt", recommendExtValue.toString());
    }

    /**
     * 添加信息流内容ID参数
     *
     * <p>根据不同的页面类型添加相应的内容ID参数。</p>
     *
     * @param recommendExtValue 推荐扩展参数构建器
     * @param reqProperty 请求属性对象
     */
    private void addFeedsContentIdParam(StringBuilder recommendExtValue, FeedsListReqProperty reqProperty) {
        if ("push_detail_2N".equals(reqProperty.getSpageID())) {
            // push沉浸式落地页上滑
            recommendExtValue.append("iid=")
                    .append(reqProperty.getPushDocId())
                    .append("&");
        } else if (StringUtils.isNotBlank(reqProperty.getRootGid())) {
            // 其他内容ID传递
            recommendExtValue.append("iid=")
                    .append(reqProperty.getRootGid())
                    .append("&");
        }
    }

    /**
     * 添加信息流游戏数量参数（版本控制）
     *
     * <p>当客户端版本大于等于52000时，添加游戏数量参数。</p>
     *
     * @param recommendExtValue 推荐扩展参数构建器
     * @param reqProperty 请求属性对象
     */
    private void addFeedsGameNumParam(StringBuilder recommendExtValue, FeedsListReqProperty reqProperty) {
        if (reqProperty.getVersion() >= 52000) {
            recommendExtValue.append("&gameNum=").append(youliApiConfig.getGameNum());
        }
    }

    /**
     * 条件添加信息流参数到Map中
     *
     * <p>只有当值不为空时才将参数添加到Map中，避免添加无效的空值参数。
     * 这是一个通用的工具方法，消除了代码中多处重复的条件判断。</p>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>消除了多处重复的 StringUtils.isNotEmpty() 判断</li>
     *   <li>提供了统一的参数添加逻辑</li>
     *   <li>降低了主方法的圈复杂度</li>
     *   <li>提高了代码的一致性和可维护性</li>
     * </ul>
     *
     * @param params 参数Map
     * @param key 参数键
     * @param value 参数值，只有非空时才会被添加
     */
    private void addFeedsParamIfNotEmpty(Map<String, Object> params, String key, String value) {
        if (StringUtils.isNotEmpty(value)) {
            params.put(key, value);
        }
    }

}

