package com.heytap.video.youli.spi.search;

import com.alibaba.fastjson.JSON;
import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.heytap.video.thirdparty.spi.search.VideoSearchLongSPI;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.model.search.LongSearchHttpResponse;
import com.heytap.video.youli.model.search.LongSearchResult;
import com.oppo.browser.video.common.pubobj.constant.FeedsItemEnum;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Item;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideo;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoActor;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoInterveneCard;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoRecommend;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoSeries;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoTag;
import com.oppo.browser.video.common.pubobj.resultObj.longvideo.LongContentSource;
import com.oppo.cpc.video.framework.lib.exception.InvalidDataRuntimeException;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;
import com.oppo.cpc.video.framework.lib.thirdparty.spi.AbstractYamlHttpSPI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.oppo.cpc.video.framework.lib.thirdparty.constant.OutCodeConstant.THIRD_PARTY_API_RESP_EXCEPTION;

/**
 * youli源长视频搜索,根据关键词获取长视频
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class YouliVideoSearchLongSPI extends AbstractYamlHttpSPI<ListBaseReqProperty, FeedsList, Serializable>
        implements VideoSearchLongSPI<FeedsList, Serializable> {
    @Autowired
    private YouliApiConfig youliApiConfig;

    @Override
    public String getApiConfigName() {
        return ApiConfigNameEnum.SEARCHLONG.getName();
    }

    @Override
    public Map<String, String> getParams(SPIContext<ListBaseReqProperty, Serializable> context) {
        ListBaseReqProperty request = context.getRequest();
        Map<String, String> params = new HashMap<>();
        if (request.getVersion() < 52300) {
            int longNum = Math.min(NumberUtils.toInt(request.getAttachments().get("longNum"), 1), 10);
            request.setLimit(longNum);
            request.setPage(1);
        } else if (request.getSearchType() == 1 && request.getPage() == 1) {
            request.setLimit(youliApiConfig.getAllSearchLongNum());
        }
        params.put("keyword", request.getKeyword());
        params.put("searchType", String.valueOf(request.getSearchType()));
        params.put("pageIndex", String.valueOf(request.getPage()));
        params.put("pageSize", String.valueOf(request.getLimit()));
        params.put("vipType", request.getVipType());
        params.put("version", String.valueOf(request.getVersion()));
        params.put("appId", request.getAppId());
        params.put("requestId", request.getRequestId());
        params.put("session", request.getSession());
        params.put("feedssession", request.getFeedssession());
        params.put("deviceType", getDeviceType(request));
        params.put("dv", request.getAttributeValues().getPhone());
        params.put("buuid", String.valueOf(request.getAttributeValues().getBuuid()));
        params.put("f", "json");
        params.put("quickEngineVersion", String.valueOf(request.getQuickEngineVersion()));
        params.put("lastSearchTab", request.getLastSearchTab());
        return params;
    }

    public String getDeviceType(ListBaseReqProperty request) {
        try {
            Integer type = request.getAttributeValues().getDeviceType();
            if (type == null || type == 5) {
                return "0";
            }
            return String.valueOf(type);
        } catch (Exception e) {
            log.error("getDeviceType error", e);
            return "0";
        }
    }

    @Override
    public VideoBaseRt<FeedsList> handleResult(SPIContext<ListBaseReqProperty, Serializable> context) {
        LongSearchHttpResponse longSearch = parseResponse(context);
        validateResponse(longSearch, context);

        FeedsList feedsList = buildBasicFeedsList(context.getRequest(), longSearch.getResult());
        populateFeedsList(feedsList, longSearch.getResult());

        return VideoBaseRt.successResponse(feedsList);
    }

    /**
     * 解析响应数据
     */
    private LongSearchHttpResponse parseResponse(SPIContext<ListBaseReqProperty, Serializable> context) {
        String response = context.getResponse();
        if (StringUtils.isEmpty(response)) {
            return null;
        }

        try {
            return JSON.parseObject(response, LongSearchHttpResponse.class);
        } catch (Exception e) {
            log.error("Failed to parse response: {}", response, e);
            return null;
        }
    }

    /**
     * 验证响应数据
     */
    private void validateResponse(LongSearchHttpResponse longSearch, SPIContext<ListBaseReqProperty, Serializable> context) {
        if (longSearch == null || longSearch.getRet() != 0 || longSearch.getResult() == null) {
            log.warn("getLongSearchResult error, the request:{}, the result:{}",
                    context.getRequest(), context.getResponse());
            String errorCode = longSearch == null ? THIRD_PARTY_API_RESP_EXCEPTION : Integer.toString(longSearch.getRet());
            throw new InvalidDataRuntimeException("getLongSearchResult error", errorCode);
        }
    }

    /**
     * 构建基础的FeedsList对象
     */
    private FeedsList buildBasicFeedsList(ListBaseReqProperty request, LongSearchResult result) {
        FeedsList feedsList = new FeedsList();
        feedsList.setOffset(request.getOffset() + result.getPageSize());
        feedsList.setHasMore(result.getHasMore() > 0);
        feedsList.setSearchTab(result.getSearchTab());
        return feedsList;
    }

    /**
     * 填充FeedsList的各种内容
     */
    private void populateFeedsList(FeedsList feedsList, LongSearchResult result) {
        processLongVideoSearchResult(feedsList, result.getLongVideoSearchResult());
        processLongVideoRecommend(feedsList, result.getLongVideoRecommend());
        processLongVideoSeries(feedsList, result.getLongVideoSeries());
        processLongVideoTag(feedsList, result.getLongVideoTag());
        processLongVideoBannerList(feedsList, result.getLongVideoBannerList());
        processLongVideoDefaultRecommend(feedsList, result.getLongVideoDefaultRecommend());
        processLongVideoActor(feedsList, result.getLongVideoActor());
    }


    /**
     * 处理长视频搜索结果
     */
    private void processLongVideoSearchResult(FeedsList feedsList, List<LongVideo> longVideoSearchResult) {
        if (CollectionUtils.isEmpty(longVideoSearchResult)) {
            return;
        }

        for (LongVideo longVideo : longVideoSearchResult) {
            if (longVideo == null) {
                continue;
            }
            searchItemToLongVideo(longVideo);
            Item item = createFeedsItem(longVideo.getSid());
            feedsList.addLongVideo(longVideo);
            feedsList.addItem(item);
        }
    }

    /**
     * 处理长视频推荐内容
     */
    private void processLongVideoRecommend(FeedsList feedsList, LongVideoRecommend longVideoRecommend) {
        if (!hasValidContents(longVideoRecommend)) {
            return;
        }

        processLongVideoContents(longVideoRecommend.getContents());
        feedsList.setLongVideoRecommend(longVideoRecommend);
    }

    /**
     * 处理长视频系列内容
     */
    private void processLongVideoSeries(FeedsList feedsList, LongVideoSeries longVideoSeries) {
        if (!hasValidContents(longVideoSeries)) {
            return;
        }

        processLongVideoContents(longVideoSeries.getContents());
        feedsList.setLongVideoSeries(longVideoSeries);
    }

    /**
     * 处理长视频标签内容
     */
    private void processLongVideoTag(FeedsList feedsList, LongVideoTag longVideoTag) {
        if (!hasValidContents(longVideoTag)) {
            return;
        }

        processLongVideoContents(longVideoTag.getContents());
        feedsList.setLongVideoTag(longVideoTag);
    }

    /**
     * 处理长视频Banner列表
     */
    private void processLongVideoBannerList(FeedsList feedsList, List<LvDrawerItemVO> longVideoBannerList) {
        if (CollectionUtils.isEmpty(longVideoBannerList)) {
            return;
        }

        List<LongVideo> bannerList = new ArrayList<>();
        for (LvDrawerItemVO lvDrawerItemVO : longVideoBannerList) {
            if (lvDrawerItemVO == null) {
                continue;
            }
            LongVideo longVideo = createBannerLongVideo(lvDrawerItemVO);
            bannerList.add(longVideo);
        }
        feedsList.setLongVideoBannerList(bannerList);
    }

    /**
     * 处理长视频默认推荐内容
     */
    private void processLongVideoDefaultRecommend(FeedsList feedsList, LongVideoInterveneCard longVideoDefaultRecommend) {
        if (!hasValidContents(longVideoDefaultRecommend)) {
            return;
        }

        processLongVideoContents(longVideoDefaultRecommend.getContents());
        feedsList.setLongVideoDefaultRecommend(longVideoDefaultRecommend);
    }

    /**
     * 处理长视频演员内容
     */
    private void processLongVideoActor(FeedsList feedsList, LongVideoActor longVideoActor) {
        if (!hasValidContents(longVideoActor)) {
            return;
        }

        processLongVideoContents(longVideoActor.getContents());
        feedsList.setLongVideoActor(longVideoActor);
    }

    /**
     * 检查对象是否有有效的内容列表
     */
    private boolean hasValidContents(Object container) {
        if (container == null) {
            return false;
        }

        List<LongVideo> contents = null;
        if (container instanceof LongVideoRecommend) {
            contents = ((LongVideoRecommend) container).getContents();
        } else if (container instanceof LongVideoSeries) {
            contents = ((LongVideoSeries) container).getContents();
        } else if (container instanceof LongVideoTag) {
            contents = ((LongVideoTag) container).getContents();
        } else if (container instanceof LongVideoInterveneCard) {
            contents = ((LongVideoInterveneCard) container).getContents();
        } else if (container instanceof LongVideoActor) {
            contents = ((LongVideoActor) container).getContents();
        }

        return CollectionUtils.isNotEmpty(contents);
    }

    /**
     * 处理长视频内容列表
     */
    private void processLongVideoContents(List<LongVideo> contents) {
        if (CollectionUtils.isEmpty(contents)) {
            return;
        }

        for (LongVideo longVideo : contents) {
            if (longVideo != null) {
                searchItemToLongVideo(longVideo);
            }
        }
    }

    /**
     * 创建FeedsItem
     */
    private Item createFeedsItem(String sid) {
        Item item = new Item();
        item.setId(sid);
        item.setMap(FeedsItemEnum.LONGVIDEO.getMap());
        item.setType(FeedsItemEnum.LONGVIDEO.getType());
        return item;
    }

    /**
     * 创建Banner类型的LongVideo
     */
    private LongVideo createBannerLongVideo(LvDrawerItemVO lvDrawerItemVO) {
        LongVideo longVideo = new LongVideo();
        longVideo.setContentType("banner");

        if (StringUtils.isNotEmpty(lvDrawerItemVO.getImgUrl())) {
            List<String> imageList = new ArrayList<>();
            imageList.add(lvDrawerItemVO.getImgUrl());
            longVideo.setHorizontalIcon(imageList);
        }

        longVideo.setDeepLink(lvDrawerItemVO.getDeepLink());
        return longVideo;
    }

    private void searchItemToLongVideo(LongVideo longVideo) {
        if (longVideo == null) {
            return;
        }

        longVideo.setId(longVideo.getSid());
        longVideo.setAlgSource("longvideo");

        if (CollectionUtils.isNotEmpty(longVideo.getMultipleSourceCode())) {
            List<LongContentSource> sourceList = longVideo.getMultipleSourceCode().stream()
                    .filter(StringUtils::isNotEmpty)
                    .map(LongContentSource::new)
                    .collect(Collectors.toList());
            longVideo.setSourceList(sourceList);
        }
    }
}