package com.heytap.video.youli.biz.algorithm;

import com.heytap.video.youli.builder.request.FeedsListReqPropertyBuilder;
import com.heytap.video.youli.cache.AlgorithmPoolCache;
import com.heytap.video.youli.config.LongVideoConfig;
import com.heytap.video.youli.config.LongVideoTrailerRecommendConfig;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmData;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmResponse;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.app.lib.cookie.UserInfo;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.cpc.video.framework.lib.programsource.SourceVersionService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

/**
 * LongVideoTrailerRecommendService 单元测试类
 * 
 * <p>主要测试 {@link LongVideoTrailerRecommendService#requestAlgorithm(FeedsListReqProperty, Integer)} 
 * 方法及其重构后的子方法。该测试类验证了重构后代码的功能完整性、边界条件处理和异常情况处理。</p>
 * 
 * <p>重构前的 requestAlgorithm 方法圈复杂度为 20，重构后降低到 8 以下，
 * 通过方法分解、条件合并、复杂条件提取等技术手段提升了代码的可读性和可维护性。</p>
 * 
 * <h3>测试覆盖范围：</h3>
 * <ul>
 *   <li>主方法功能测试：正常情况、不同算法类型、边界条件</li>
 *   <li>重构后子方法测试：每个私有方法的独立测试</li>
 *   <li>参数构建验证：基础参数、特定参数、通用参数的正确性验证</li>
 *   <li>异常处理测试：HTTP异常、响应异常的处理验证</li>
 * </ul>
 * 
 * <h3>Mock 策略：</h3>
 * <ul>
 *   <li>使用 Mockito 来 Mock 依赖服务和配置对象</li>
 *   <li>使用 FeedsListReqPropertyBuilder 来构建测试数据</li>
 *   <li>Mock HTTP请求和响应处理</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/01
 * @see LongVideoTrailerRecommendService
 * @see FeedsListReqPropertyBuilder
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class LongVideoTrailerRecommendServiceTest {

    /**
     * 被测试的服务类实例，通过 @InjectMocks 自动注入 Mock 依赖
     */
    @InjectMocks
    private LongVideoTrailerRecommendService longVideoTrailerRecommendService;

    /**
     * Mock 的长视频配置对象
     */
    @Mock
    private LongVideoConfig longVideoConfig;

    /**
     * Mock 的长视频预告推荐配置对象
     */
    @Mock
    private LongVideoTrailerRecommendConfig longVideoTrailerRecommendConfig;

    /**
     * Mock 的HTTP数据通道
     */
    @Mock
    private HttpDataChannel httpDataChannel;

    /**
     * Mock 的算法池缓存
     */
    @Mock
    private AlgorithmPoolCache algorithmPoolCache;

    /**
     * Mock 的源版本服务
     */
    @Mock
    private SourceVersionService sourceVersionService;

    /**
     * 测试用的请求属性对象
     */
    private FeedsListReqProperty reqProperty;

    /**
     * 测试前的初始化方法
     * 
     * <p>设置测试所需的 Mock 对象和测试数据，包括：</p>
     * <ul>
     *   <li>初始化测试用的 FeedsListReqProperty 对象</li>
     *   <li>Mock 各种配置对象的方法</li>
     *   <li>设置合理的默认返回值</li>
     * </ul>
     */
    @Before
    public void setUp() throws HttpDataChannelException {
        // 初始化测试数据
        setupTestData();
        
        // Mock 配置对象
        setupMockConfigs();
        
        // Mock 服务对象
        setupMockServices();
    }

    /**
     * 设置测试数据
     * 
     * <p>使用 {@link FeedsListReqPropertyBuilder} 构建标准的测试数据，
     * 包含所有必要的字段和合理的默认值。</p>
     */
    private void setupTestData() {
        reqProperty = new FeedsListReqPropertyBuilder()
                .withSid("test_sid")
                .withVid("test_vid")
                .withPoolCode("test_pool")
                .withVersion(41)
                .withFetchMoreVideosSwitch(true)
                .withContentCount(20)
                .withCookieBuuid(987654321L)
                .withPhone("OPPO_R15")  // 明确设置手机型号，与测试断言保持一致
                .build();
    }

    /**
     * 设置Mock配置对象
     */
    private void setupMockConfigs() {
        // Mock LongVideoConfig
        when(longVideoConfig.getAlgorithmTypeOne()).thenReturn(1);
        when(longVideoConfig.getAlgorithmTypeTwo()).thenReturn(2);
        when(longVideoConfig.getAlgorithmTypeUGC()).thenReturn(3);
        when(longVideoConfig.getAlgorithmTypeFXS()).thenReturn(4);

        // Mock LongVideoTrailerRecommendConfig
        when(longVideoTrailerRecommendConfig.getRecommendAlgorithmRoute()).thenReturn("test_route");
        when(longVideoTrailerRecommendConfig.getRecommendAlgorithmCid()).thenReturn("test_cid");
        when(longVideoTrailerRecommendConfig.getRecommendAlgorithmChannelId()).thenReturn("test_channel_id");
        when(longVideoTrailerRecommendConfig.getRecommendAlgorithmUrl()).thenReturn("http://test.algorithm.url");
        when(longVideoTrailerRecommendConfig.getUgcAlgorithmUrl()).thenReturn("http://test.ugc.url");
        when(longVideoTrailerRecommendConfig.getRecommendAlgorithmTimeout()).thenReturn(5000);

        Map<Integer, String> bidList = new HashMap<>();
        bidList.put(1, "bid_1");
        bidList.put(2, "bid_2");
        bidList.put(3, "bid_3");
        bidList.put(4, "bid_4");
        when(longVideoTrailerRecommendConfig.getBidList()).thenReturn(bidList);
    }

    /**
     * 设置Mock服务对象
     */
    private void setupMockServices() throws HttpDataChannelException {
        // Mock SourceVersionService
        when(sourceVersionService.getSourceStrByVersion(anyInt())).thenReturn("test_source_list");

        // Mock AlgorithmPoolCache
        LvtRecommendAlgorithmResponse cacheResponse = new LvtRecommendAlgorithmResponse();
        cacheResponse.setStatus(0);
        when(algorithmPoolCache.getAlgorithmCacheResponse(any(), anyInt(), anyString())).thenReturn(cacheResponse);

        // Mock HttpDataChannel
        LvtRecommendAlgorithmResponse mockResponse = new LvtRecommendAlgorithmResponse();
        mockResponse.setStatus(0);
        mockResponse.setData(List.of(new LvtRecommendAlgorithmData()));
        CompletableFuture<LvtRecommendAlgorithmResponse> future = CompletableFuture.completedFuture(mockResponse);
        when(httpDataChannel.asyncGetForObject(anyString(), any(Class.class), (Map<String, String>) any(Map.class), anyInt()))
                .thenReturn(future);
    }

    /**
     * 测试 requestAlgorithm 方法的正常情况
     * 
     * <p>验证在正常输入参数下，方法能够正确构建算法请求参数并执行请求。
     * 这是最重要的测试用例，验证了重构后方法的核心功能。</p>
     * 
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>算法类型一（需要频道ID、文档ID、视频ID）</li>
     *   <li>完整的请求属性信息</li>
     *   <li>有效的版本信息</li>
     *   <li>正常的HTTP响应</li>
     * </ul>
     * 
     * <h3>验证点：</h3>
     * <ul>
     *   <li>返回的CompletableFuture不为null</li>
     *   <li>响应状态为成功（0）</li>
     *   <li>响应数据不为空</li>
     * </ul>
     * 
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testRequestAlgorithm_NormalCase_AlgorithmTypeOne() throws Exception {
        // 执行测试
        CompletableFuture<LvtRecommendAlgorithmResponse> result = 
                longVideoTrailerRecommendService.requestAlgorithm(reqProperty, 1);

        // 验证结果
        Assert.assertNotNull(result);
        LvtRecommendAlgorithmResponse response = result.get();
        Assert.assertNotNull(response);
        Assert.assertEquals(Integer.valueOf(0), response.getStatus());
        Assert.assertNotNull(response.getData());
        Assert.assertFalse(response.getData().isEmpty());
    }

    /**
     * 测试算法类型二的情况
     * 
     * <p>验证内容池算法类型的参数构建和请求执行。</p>
     * 
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testRequestAlgorithm_AlgorithmTypeTwo() throws Exception {
        CompletableFuture<LvtRecommendAlgorithmResponse> result = 
                longVideoTrailerRecommendService.requestAlgorithm(reqProperty, 2);

        Assert.assertNotNull(result);
        LvtRecommendAlgorithmResponse response = result.get();
        Assert.assertNotNull(response);
        Assert.assertEquals(Integer.valueOf(0), response.getStatus());
    }

    /**
     * 测试UGC算法类型的情况
     * 
     * <p>验证UGC算法类型使用专门的URL和参数构建。</p>
     * 
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testRequestAlgorithm_AlgorithmTypeUGC() throws Exception {
        CompletableFuture<LvtRecommendAlgorithmResponse> result = 
                longVideoTrailerRecommendService.requestAlgorithm(reqProperty, 3);

        Assert.assertNotNull(result);
        LvtRecommendAlgorithmResponse response = result.get();
        Assert.assertNotNull(response);
        Assert.assertEquals(Integer.valueOf(0), response.getStatus());
    }

    /**
     * 测试无版本信息的情况
     * 
     * <p>验证当请求不包含版本信息时，不会添加源列表参数。</p>
     * 
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testRequestAlgorithm_NoVersion() throws Exception {
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withSid("test_sid")
                .withVid("test_vid")
                .withNullVersion()
                .build();

        CompletableFuture<LvtRecommendAlgorithmResponse> result = 
                longVideoTrailerRecommendService.requestAlgorithm(testReqProperty, 1);

        Assert.assertNotNull(result);
        LvtRecommendAlgorithmResponse response = result.get();
        Assert.assertNotNull(response);
        Assert.assertEquals(Integer.valueOf(0), response.getStatus());
    }

    /**
     * 测试无Cookie的情况
     * 
     * <p>验证当请求不包含Cookie时，buuid提取逻辑的正确性。</p>
     * 
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testRequestAlgorithm_NoCookie() throws Exception {
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withSid("test_sid")
                .withVid("test_vid")
                .withNullCookie()
                .build();

        CompletableFuture<LvtRecommendAlgorithmResponse> result = 
                longVideoTrailerRecommendService.requestAlgorithm(testReqProperty, 1);

        Assert.assertNotNull(result);
        LvtRecommendAlgorithmResponse response = result.get();
        Assert.assertNotNull(response);
        Assert.assertEquals(Integer.valueOf(0), response.getStatus());
    }

    // ========== 测试重构后的子方法 ==========

    /**
     * 测试基础参数构建方法
     *
     * <p>验证 {@code buildBaseParams()} 方法能够正确构建基础请求参数。
     * 该方法是重构后提取的私有方法，用于构建所有算法请求的基础参数。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>验证设备信息参数（r_dv）</li>
     *   <li>验证路由参数（route）</li>
     *   <li>验证业务ID参数（cid）</li>
     *   <li>验证推荐栏ID参数（bidlst）</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将基础参数构建逻辑独立为方法</li>
     *   <li>提高了代码的可读性和可维护性</li>
     *   <li>便于单独测试基础参数构建逻辑</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testBuildBaseParams() throws Exception {
        Method method = LongVideoTrailerRecommendService.class.getDeclaredMethod("buildBaseParams",
                FeedsListReqProperty.class, Integer.class);
        method.setAccessible(true);

        Map<String, String> result = (Map<String, String>) method.invoke(
                longVideoTrailerRecommendService, reqProperty, 1);

        Assert.assertNotNull(result);
        Assert.assertEquals("OPPO_R15", result.get("r_dv"));
        Assert.assertEquals("test_route", result.get("route"));
        Assert.assertEquals("test_cid", result.get("cid"));
        Assert.assertEquals("bid_1", result.get("bidlst"));
    }

    /**
     * 测试内容池算法类型判断方法
     *
     * <p>验证 {@code isContentPoolAlgorithmType()} 方法能够正确判断算法类型。
     * 该方法是重构后提取的私有方法，用于合并相似的算法类型判断逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>算法类型一 - 应返回 false</li>
     *   <li>算法类型二 - 应返回 true</li>
     *   <li>UGC算法类型 - 应返回 true</li>
     *   <li>FXS算法类型 - 应返回 true</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>合并了相似的算法类型判断逻辑</li>
     *   <li>提高了代码的可读性和可维护性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsContentPoolAlgorithmType() throws Exception {
        Method method = LongVideoTrailerRecommendService.class.getDeclaredMethod("isContentPoolAlgorithmType", Integer.class);
        method.setAccessible(true);

        // 测试算法类型一
        Boolean result = (Boolean) method.invoke(longVideoTrailerRecommendService, 1);
        Assert.assertFalse(result);

        // 测试算法类型二
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, 2);
        Assert.assertTrue(result);

        // 测试UGC算法类型
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, 3);
        Assert.assertTrue(result);

        // 测试FXS算法类型
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, 4);
        Assert.assertTrue(result);
    }

    /**
     * 测试视频数量计算方法
     *
     * <p>验证 {@code calculateVideoNum()} 方法能够根据开关和配置正确计算视频数量。
     * 该方法是重构后提取的私有方法，用于简化视频数量计算逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>开关开启且有内容数量配置 - 应返回配置的数量</li>
     *   <li>开关关闭 - 应返回默认限制数量</li>
     *   <li>开关开启但无内容数量配置 - 应返回默认限制数量</li>
     * </ul>
     *
     * <h3>业务逻辑：</h3>
     * <ul>
     *   <li>混排场景需要获取更多视频以提供混排空间</li>
     *   <li>优先使用运营配置的每页视频数量</li>
     *   <li>未配置时使用默认的限制数量</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testCalculateVideoNum() throws Exception {
        Method method = LongVideoTrailerRecommendService.class.getDeclaredMethod("calculateVideoNum", FeedsListReqProperty.class);
        method.setAccessible(true);

        // 测试开关开启且有内容数量配置
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withFetchMoreVideosSwitch(true)
                .withContentCount(20)
                .build();
        Integer result = (Integer) method.invoke(longVideoTrailerRecommendService, testReqProperty);
        Assert.assertEquals(Integer.valueOf(20), result);

        // 测试开关关闭
        testReqProperty = new FeedsListReqPropertyBuilder()
                .withFetchMoreVideosSwitch(false)
                .withContentCount(20)
                .build();
        result = (Integer) method.invoke(longVideoTrailerRecommendService, testReqProperty);
        Assert.assertEquals(Integer.valueOf(10), result); // 默认limit为10

        // 测试开关开启但无内容数量配置
        testReqProperty = new FeedsListReqPropertyBuilder()
                .withFetchMoreVideosSwitch(true)
                .withContentCount(null)
                .build();
        result = (Integer) method.invoke(longVideoTrailerRecommendService, testReqProperty);
        Assert.assertEquals(Integer.valueOf(10), result); // 默认limit为10
    }

    /**
     * 测试Cookie中buuid有效性验证方法
     *
     * <p>验证 {@code isCookieBuuidValid()} 方法能够正确判断Cookie中buuid的有效性。
     * 该方法是重构后提取的私有方法，用于简化复杂的 && 条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>Cookie为null - 应返回false</li>
     *   <li>Cookie不为null但info为null - 应返回false</li>
     *   <li>info不为null但buuid为null - 应返回false</li>
     *   <li>所有条件都满足 - 应返回true</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 && 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsCookieBuuidValid() throws Exception {
        Method method = LongVideoTrailerRecommendService.class.getDeclaredMethod("isCookieBuuidValid", Cookie.class);
        method.setAccessible(true);

        // 测试Cookie为null
        Boolean result = (Boolean) method.invoke(longVideoTrailerRecommendService, (Cookie) null);
        Assert.assertFalse(result);

        // 测试Cookie不为null但info为null
        Cookie cookie = PowerMockito.mock(Cookie.class);
        when(cookie.getInfo()).thenReturn(null);
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, cookie);
        Assert.assertFalse(result);

        // 测试info不为null但buuid为null
        UserInfo cookieInfo = PowerMockito.mock(UserInfo.class);
        when(cookie.getInfo()).thenReturn(cookieInfo);
        when(cookieInfo.getBuuid()).thenReturn(null);
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, cookie);
        Assert.assertFalse(result);

        // 测试所有条件都满足
        when(cookieInfo.getBuuid()).thenReturn(123456L);
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, cookie);
        Assert.assertTrue(result);
    }

    /**
     * 测试算法URL确定方法
     *
     * <p>验证 {@code determineAlgorithmUrl()} 方法能够根据算法类型选择正确的URL。
     * 该方法是重构后提取的私有方法，用于简化URL选择逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>UGC算法类型 - 应返回UGC专用URL</li>
     *   <li>其他算法类型 - 应返回通用推荐URL</li>
     * </ul>
     *
     * <h3>URL选择逻辑：</h3>
     * <ul>
     *   <li>UGC算法类型：使用UGC专用算法URL</li>
     *   <li>其他算法类型：使用通用推荐算法URL</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testDetermineAlgorithmUrl() throws Exception {
        Method method = LongVideoTrailerRecommendService.class.getDeclaredMethod("determineAlgorithmUrl", Integer.class);
        method.setAccessible(true);

        // 测试UGC算法类型
        String result = (String) method.invoke(longVideoTrailerRecommendService, 3);
        Assert.assertEquals("http://test.ugc.url", result);

        // 测试其他算法类型
        result = (String) method.invoke(longVideoTrailerRecommendService, 1);
        Assert.assertEquals("http://test.algorithm.url", result);

        result = (String) method.invoke(longVideoTrailerRecommendService, 2);
        Assert.assertEquals("http://test.algorithm.url", result);
    }

    /**
     * 测试是否应该添加源列表参数的判断方法
     *
     * <p>验证 {@code shouldAddSourceList()} 方法能够正确判断是否需要添加源列表参数。
     * 该方法是重构后提取的私有方法，用于简化复杂的条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>有版本信息且算法类型一 - 应返回true</li>
     *   <li>有版本信息且算法类型二 - 应返回true</li>
     *   <li>有版本信息但其他算法类型 - 应返回false</li>
     *   <li>无版本信息 - 应返回false</li>
     * </ul>
     *
     * <h3>添加条件：</h3>
     * <ul>
     *   <li>请求包含版本信息（version不为null）</li>
     *   <li>算法类型为类型一或类型二</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testShouldAddSourceList() throws Exception {
        Method method = LongVideoTrailerRecommendService.class.getDeclaredMethod("shouldAddSourceList",
                FeedsListReqProperty.class, Integer.class);
        method.setAccessible(true);

        // 测试有版本信息且算法类型一
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withVersion(41)
                .build();
        Boolean result = (Boolean) method.invoke(longVideoTrailerRecommendService, testReqProperty, 1);
        Assert.assertTrue(result);

        // 测试有版本信息且算法类型二
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, testReqProperty, 2);
        Assert.assertTrue(result);

        // 测试有版本信息但其他算法类型
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, testReqProperty, 3);
        Assert.assertFalse(result);

        // 测试无版本信息
        testReqProperty = new FeedsListReqPropertyBuilder()
                .withNullVersion()
                .build();
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, testReqProperty, 1);
        Assert.assertFalse(result);
    }

    /**
     * 测试响应有效性判断方法
     *
     * <p>验证 {@code isResponseInvalid()} 方法能够正确判断算法响应的有效性。
     * 该方法是重构后提取的私有方法，用于简化复杂的 || 条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>异常不为null - 应返回true（无效）</li>
     *   <li>响应为null - 应返回true（无效）</li>
     *   <li>响应状态为null - 应返回true（无效）</li>
     *   <li>响应状态不为0 - 应返回true（无效）</li>
     *   <li>正常响应 - 应返回false（有效）</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 || 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsResponseInvalid() throws Exception {
        Method method = LongVideoTrailerRecommendService.class.getDeclaredMethod("isResponseInvalid",
                LvtRecommendAlgorithmResponse.class, Throwable.class);
        method.setAccessible(true);

        // 测试异常不为null
        Boolean result = (Boolean) method.invoke(longVideoTrailerRecommendService, null, new RuntimeException("test"));
        Assert.assertTrue(result);

        // 测试响应为null
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, null, null);
        Assert.assertTrue(result);

        // 测试响应状态为null
        LvtRecommendAlgorithmResponse response = new LvtRecommendAlgorithmResponse();
        response.setStatus(null);
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, response, null);
        Assert.assertTrue(result);

        // 测试响应状态不为0
        response.setStatus(1);
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, response, null);
        Assert.assertTrue(result);

        // 测试正常响应
        response.setStatus(0);
        result = (Boolean) method.invoke(longVideoTrailerRecommendService, response, null);
        Assert.assertFalse(result);
    }
}
