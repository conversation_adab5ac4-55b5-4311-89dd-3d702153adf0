package com.heytap.video.youli.biz.algorithm;

import com.heytap.video.youli.builder.request.FeedsListReqPropertyBuilder;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.utils.BizUtils;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.app.lib.cookie.UserInfo;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.utils.Env;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.app.lib.strategy.IPLocationStrategyResult;
import com.oppo.browser.video.common.pubobj.constant.RecTypeConstant;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.trace.core.TraceThreadLocal;
import com.oppo.trace.core.scope.TraceScope;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static com.heytap.video.youli.constant.GlobalConstant.*;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * RecommendService 信息流推荐单元测试类
 * 
 * <p>主要测试 {@link RecommendService#getReqParams(FeedsListReqProperty)} 
 * 方法及其重构后的子方法。该测试类验证了重构后代码的功能完整性、边界条件处理和异常情况处理。</p>
 * 
 * <p>重构前的 getReqParams 方法圈复杂度为 21，重构后降低到 6 以下，
 * 通过方法分解、逻辑分层、条件提取等技术手段提升了代码的可读性和可维护性。</p>
 * 
 * <h3>测试覆盖范围：</h3>
 * <ul>
 *   <li>主方法功能测试：正常情况、不同推荐类型、边界条件</li>
 *   <li>重构后子方法测试：每个私有方法的独立测试</li>
 *   <li>参数构建验证：基础参数、推荐参数、设备参数、扩展参数的正确性验证</li>
 *   <li>环境差异测试：开发、测试、生产环境的参数差异验证</li>
 * </ul>
 * 
 * <h3>Mock 策略：</h3>
 * <ul>
 *   <li>使用 PowerMock 来 Mock 静态方法（Env、TraceThreadLocal、JsonTools、BizUtils）</li>
 *   <li>使用 Mockito 来 Mock 复杂对象（Cookie、YouliApiConfig）</li>
 *   <li>使用 FeedsListReqPropertyBuilder 来构建测试数据</li>
 * </ul>
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @since 2025-01-01
 * @see RecommendService
 * @see FeedsListReqPropertyBuilder
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({Env.class, TraceThreadLocal.class, TraceScope.class, BizUtils.class, JsonTools.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class RecommendServiceFeedsTest {

    /**
     * 被测试的服务类实例，通过 @InjectMocks 自动注入 Mock 依赖
     */
    @InjectMocks
    private RecommendService recommendService;

    /**
     * Mock 的配置类，用于提供测试所需的配置参数
     */
    @Mock
    private YouliApiConfig youliApiConfig;

    /**
     * 测试用的请求属性对象
     */
    private FeedsListReqProperty reqProperty;

    /**
     * 测试前的初始化方法
     * 
     * <p>设置测试所需的 Mock 对象和测试数据，包括：</p>
     * <ul>
     *   <li>初始化测试用的 FeedsListReqProperty 对象</li>
     *   <li>Mock YouliApiConfig 的配置方法</li>
     *   <li>Mock BizUtils 的静态方法</li>
     * </ul>
     */
    @Before
    public void setUp() {
        // 初始化测试数据
        setupTestData();
        
        // Mock YouliApiConfig
        when(youliApiConfig.getEachRefreshNum()).thenReturn(10);
        when(youliApiConfig.getGameNum()).thenReturn(3);
        
        Map<String, String> recBidLstMap = new HashMap<>();
        recBidLstMap.put("test_from_id", "test_bidlst");
        when(youliApiConfig.getRecBidLstMap()).thenReturn(recBidLstMap);
        
        Map<String, String> recChannelIdMap = new HashMap<>();
        recChannelIdMap.put("test_from_id", "test_channel_id");
        when(youliApiConfig.getRecChannelIdMap()).thenReturn(recChannelIdMap);
        
        // Mock BizUtils
        PowerMockito.mockStatic(BizUtils.class);
        when(BizUtils.getRelevantBidLst(any(YouliApiConfig.class))).thenReturn("test_bidlst");
    }

    /**
     * 设置测试数据
     * 
     * <p>使用 {@link FeedsListReqPropertyBuilder} 构建标准的测试数据，
     * 包含所有必要的字段和合理的默认值。</p>
     */
    private void setupTestData() {
        reqProperty = new FeedsListReqPropertyBuilder()
                .withRequestId("test_request_id")
                .withFromId("test_from_id")
                .withType("video")
                .withOffset(0)
                .withDownTimes(1)
                .withUpTimes(0)
                .withArea("{\"country\":\"China\",\"province\":\"Beijing\",\"city\":\"Beijing\"}")
                .withSpageID("test_spage_id")
                .withRootGid("test_root_gid")
                .withVersion(52000)
                .withPhone("OPPO_R15")
                .withCookieBuuid(987654321L)
                .build();

        // 设置默认的 AttributeValues
        if (reqProperty.getAttributeValues() == null) {
            AttributeValues attributeValues = new AttributeValues();
            attributeValues.setPhone("OPPO_R15");
            attributeValues.setNetwork("wifi");
            attributeValues.setClientFullBrowserVersion("4.1.0");
            attributeValues.setIp("***********");
            attributeValues.setBuuid(123456789L);
            attributeValues.setImei("default_imei");
            reqProperty.setAttributeValues(attributeValues);
        }
    }

    /**
     * 测试 getReqParams 方法的正常情况
     * 
     * <p>验证在正常输入参数下，方法能够正确构建信息流推荐请求参数。
     * 这是最重要的测试用例，验证了重构后方法的核心功能。</p>
     * 
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>生产环境（不添加路由参数）</li>
     *   <li>完整的用户属性信息</li>
     *   <li>有效的地理位置信息</li>
     *   <li>正常的推荐类型</li>
     *   <li>下拉刷新（offset=0）</li>
     * </ul>
     * 
     * <h3>验证点：</h3>
     * <ul>
     *   <li>必传参数：appId、cid、time、traceId、num、bidlst 等</li>
     *   <li>频道参数：r_channel_id、r_channel_type</li>
     *   <li>刷新参数：r_action、r_page、r_refresh</li>
     *   <li>地理位置参数：r_area</li>
     *   <li>设备参数：network、r_devtype、r_client_version、ip</li>
     *   <li>用户参数：personalRec、r_username</li>
     *   <li>扩展参数：needRelation、picFormatType、recommendExt、homepage</li>
     * </ul>
     * 
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testGetReqParams_NormalCase() throws Exception {
        // Mock 环境
        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        // Mock JsonTools
        PowerMockito.mockStatic(JsonTools.class);
        IPLocationStrategyResult location = new IPLocationStrategyResult();
        location.setCountry("China");
        location.setProvince("Beijing");
        location.setCity("Beijing");
        when(JsonTools.toMap(any(String.class), any(Class.class))).thenReturn(location);

        // 调用私有方法
        Method method = RecommendService.class.getDeclaredMethod("getReqParams", FeedsListReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, reqProperty);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.get(APPID));
        Assert.assertEquals("video", result.get("cid"));
        Assert.assertEquals("test_request_id", result.get(TRACEID));
        Assert.assertEquals(10, result.get("num"));
        Assert.assertEquals("test_bidlst", result.get("bidlst"));
        Assert.assertEquals("test_channel_id", result.get("r_channel_id"));
        Assert.assertEquals("video", result.get("r_channel_type"));
        Assert.assertEquals(0, result.get(R_ACTION));
        Assert.assertEquals(1, result.get(R_PAGE));
        Assert.assertEquals(1, result.get("r_refresh"));
        Assert.assertEquals("country:China;province:Beijing;city:Beijing", result.get("r_area"));
        Assert.assertEquals("wifi", result.get("network"));
        Assert.assertEquals("OPPO_R15", result.get("r_devtype"));
        Assert.assertEquals(1, result.get("personalRec"));
        Assert.assertEquals(false, result.get("needRelation"));
        Assert.assertEquals(1, result.get("picFormatType"));
        Assert.assertEquals(false, result.get("homepage"));
        Assert.assertNotNull(result.get("recommendExt"));
        Assert.assertTrue(((String) result.get("recommendExt")).contains("parFromId=test_spage_id"));
        Assert.assertTrue(((String) result.get("recommendExt")).contains("gameNum=3"));
    }

    /**
     * 测试开发环境下的路由参数
     * 
     * <p>验证在开发环境下，方法会自动添加路由参数 "bjhtonlinerec"。
     * 这是环境相关的重要功能，确保开发环境的请求能够正确路由。</p>
     * 
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>环境变量为 "dev"</li>
     *   <li>其他参数保持默认</li>
     * </ul>
     * 
     * <h3>验证点：</h3>
     * <ul>
     *   <li>route 参数存在且值为 "bjhtonlinerec"</li>
     * </ul>
     * 
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testGetReqParams_DevEnvironment() throws Exception {
        // Mock 开发环境
        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("dev");

        Method method = RecommendService.class.getDeclaredMethod("getReqParams", FeedsListReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, reqProperty);

        // 验证开发环境下的路由参数
        Assert.assertEquals("bjhtonlinerec", result.get("route"));
    }

    /**
     * 测试H5推荐类型的情况
     * 
     * <p>验证当推荐类型为H5时，会调用特殊的H5参数处理逻辑。</p>
     * 
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testGetReqParams_H5RecType() throws Exception {
        // 使用 Builder 创建 H5 类型的测试数据
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withRecType(RecTypeConstant.H5)
                .withFromId("test_from_id")
                .build();

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getReqParams", FeedsListReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, testReqProperty);

        // 验证结果包含基本参数（H5 参数处理在 appendH5Params 中，这里主要验证方法调用）
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.get(APPID));
    }

    /**
     * 测试翻页情况
     * 
     * <p>验证当offset不为0时，刷新行为参数的正确设置。</p>
     * 
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testGetReqParams_OffsetNotZero() throws Exception {
        // 使用 Builder 创建翻页的测试数据
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withOffset(10)
                .withUpTimes(2)
                .withFromId("test_from_id")
                .build();

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getReqParams", FeedsListReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, testReqProperty);

        // 验证翻页参数
        Assert.assertEquals(1, result.get(R_ACTION)); // 翻页
        Assert.assertEquals(2, result.get(R_PAGE)); // upTimes
    }

    /**
     * 测试无RequestId且有TraceScope的情况
     *
     * <p>验证当请求中没有requestId但TraceScope有效时，从TraceScope获取traceId。</p>
     *
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testGetReqParams_NoRequestId_WithTraceScope() throws Exception {
        // 创建无requestId的测试数据
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withRequestId(null)
                .withFromId("test_from_id")
                .build();

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        // Mock TraceScope
        PowerMockito.mockStatic(TraceThreadLocal.class);
        TraceScope traceScope = PowerMockito.mock(TraceScope.class);
        Map<String, String> attachments = new HashMap<>();
        attachments.put(TraceScope.ATTACHMENT_KEY.REQUEST_ID, "trace_request_id");
        when(TraceThreadLocal.getScope()).thenReturn(traceScope);
        when(traceScope.getAttachments()).thenReturn(attachments);

        Method method = RecommendService.class.getDeclaredMethod("getReqParams", FeedsListReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, testReqProperty);

        // 验证从TraceScope获取的traceId
        Assert.assertEquals("trace_request_id", result.get(TRACEID));
    }

    /**
     * 测试无RequestId且无TraceScope的情况
     *
     * <p>验证当请求中没有requestId且TraceScope无效时，生成UUID作为traceId。</p>
     *
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testGetReqParams_NoRequestId_NoTraceScope() throws Exception {
        // 创建无requestId的测试数据
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withRequestId(null)
                .withFromId("test_from_id")
                .build();

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        // Mock TraceScope为null
        PowerMockito.mockStatic(TraceThreadLocal.class);
        when(TraceThreadLocal.getScope()).thenReturn(null);

        Method method = RecommendService.class.getDeclaredMethod("getReqParams", FeedsListReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, testReqProperty);

        // 验证生成的UUID（应该是32位字符串，不包含连字符）
        String traceId = (String) result.get(TRACEID);
        Assert.assertNotNull(traceId);
        Assert.assertEquals(32, traceId.length());
        Assert.assertFalse(traceId.contains("-"));
    }

    /**
     * 测试可选字段为空的情况
     *
     * <p>验证当可选字段为空时，这些字段不会被添加到参数中。</p>
     *
     * @throws Exception 测试可能抛出的异常
     */
    @Test
    public void testGetReqParams_EmptyOptionalFields() throws Exception {
        // 创建一个 FeedsListReqProperty，确保所有可选字段都为空
        FeedsListReqProperty testReqProperty = new FeedsListReqProperty();
        testReqProperty.setFromId("test_from_id");
        testReqProperty.setArea(null); // 地理位置为空
        testReqProperty.setScookie(null); // Cookie为空

        // 创建 AttributeValues，所有可选字段都设置为null或空字符串
        AttributeValues attributeValues = new AttributeValues();
        attributeValues.setPhone(null); // 手机型号为空 -> r_devtype不会被添加
        attributeValues.setNetwork(null); // 网络为空 -> network不会被添加
        attributeValues.setClientFullBrowserVersion(null); // 客户端版本为空 -> r_client_version不会被添加
        attributeValues.setIp(null); // IP为空 -> ip不会被添加
        attributeValues.setBuuid(123456789L); // buuid必须有值，用于buuid参数
        attributeValues.setImei("default_imei"); // imei必须有值，用于imei参数
        testReqProperty.setAttributeValues(attributeValues);

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getReqParams", FeedsListReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, testReqProperty);

        // 验证可选字段不存在（因为对应的值为null或空）
        Assert.assertFalse("r_area should not exist when area is null", result.containsKey("r_area"));
        Assert.assertFalse("network should not exist when network is null", result.containsKey("network"));
        Assert.assertFalse("r_devtype should not exist when phone is null", result.containsKey("r_devtype"));
        Assert.assertFalse("r_client_version should not exist when clientFullBrowserVersion is null", result.containsKey("r_client_version"));
        Assert.assertFalse("ip should not exist when ip is null", result.containsKey("ip"));
        Assert.assertFalse("r_username should not exist when cookie is null", result.containsKey("r_username"));

        // 验证必传字段仍然存在（这些字段总是会被添加，不管值是什么）
        Assert.assertEquals(2, result.get(APPID));
        Assert.assertEquals("video", result.get("cid"));
        Assert.assertNotNull(result.get(TRACEID));
        Assert.assertEquals(123456789L, result.get("buuid")); // buuid总是存在
        Assert.assertEquals("default_imei", result.get("imei")); // imei总是存在
        Assert.assertEquals(1, result.get("personalRec")); // personalRec总是为1
    }

    // ========== 测试重构后的子方法 ==========

    /**
     * 测试信息流测试环境判断方法
     *
     * <p>验证 {@code isFeedsTestEnvironment()} 方法能够正确判断当前是否为测试环境。
     * 该方法是重构后提取的私有方法，用于简化环境判断逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>环境变量为 "dev" - 应返回 true</li>
     *   <li>环境变量为 "test" - 应返回 true</li>
     *   <li>环境变量为 "prod" - 应返回 false</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 || 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsFeedsTestEnvironment() throws Exception {
        PowerMockito.mockStatic(Env.class);

        // 测试 dev 环境
        when(Env.getEnv()).thenReturn("dev");
        Method method = RecommendService.class.getDeclaredMethod("isFeedsTestEnvironment");
        method.setAccessible(true);
        Boolean result = (Boolean) method.invoke(recommendService);
        Assert.assertTrue(result);

        // 测试 test 环境
        when(Env.getEnv()).thenReturn("test");
        result = (Boolean) method.invoke(recommendService);
        Assert.assertTrue(result);

        // 测试生产环境
        when(Env.getEnv()).thenReturn("prod");
        result = (Boolean) method.invoke(recommendService);
        Assert.assertFalse(result);
    }

    /**
     * 测试信息流追踪ID提取方法
     *
     * <p>验证 {@code extractFeedsTraceId()} 方法能够按优先级正确提取追踪ID。
     * 该方法是重构后提取的私有方法，用于简化追踪ID获取逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>有requestId - 应返回requestId</li>
     *   <li>无requestId但有TraceScope - 应返回TraceScope中的ID</li>
     *   <li>都没有 - 应返回生成的UUID</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的if-else逻辑分层处理</li>
     *   <li>提高了代码的可读性和可维护性</li>
     *   <li>便于单独测试追踪ID提取逻辑</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testExtractFeedsTraceId() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("extractFeedsTraceId", FeedsListReqProperty.class);
        method.setAccessible(true);

        // 测试有requestId的情况
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withRequestId("test_request_id")
                .build();
        String result = (String) method.invoke(recommendService, testReqProperty);
        Assert.assertEquals("test_request_id", result);

        // 测试无requestId但有TraceScope的情况
        testReqProperty = new FeedsListReqPropertyBuilder()
                .withRequestId(null)
                .build();

        PowerMockito.mockStatic(TraceThreadLocal.class);
        TraceScope traceScope = PowerMockito.mock(TraceScope.class);
        Map<String, String> attachments = new HashMap<>();
        attachments.put(TraceScope.ATTACHMENT_KEY.REQUEST_ID, "trace_id");
        when(TraceThreadLocal.getScope()).thenReturn(traceScope);
        when(traceScope.getAttachments()).thenReturn(attachments);

        result = (String) method.invoke(recommendService, testReqProperty);
        Assert.assertEquals("trace_id", result);

        // 测试都没有的情况
        when(TraceThreadLocal.getScope()).thenReturn(null);
        result = (String) method.invoke(recommendService, testReqProperty);
        Assert.assertNotNull(result);
        Assert.assertEquals(32, result.length()); // UUID去除连字符后的长度
    }

    /**
     * 测试信息流链路追踪上下文有效性验证方法
     *
     * <p>验证 {@code isFeedsTraceScopeValid()} 方法能够正确判断链路追踪上下文的有效性。
     * 该方法是重构后提取的私有方法，用于简化复杂的 && 条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>TraceScope 为 null - 应返回 false</li>
     *   <li>TraceScope 不为 null 但 attachments 为 null - 应返回 false</li>
     *   <li>attachments 不为 null 但 REQUEST_ID 为空 - 应返回 false</li>
     *   <li>所有条件都满足 - 应返回 true</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 && 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsFeedsTraceScopeValid() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("isFeedsTraceScopeValid");
        method.setAccessible(true);

        PowerMockito.mockStatic(TraceThreadLocal.class);

        // 测试 scope 为 null
        when(TraceThreadLocal.getScope()).thenReturn(null);
        Boolean result = (Boolean) method.invoke(recommendService);
        Assert.assertFalse(result);

        // 测试 scope 不为 null 但 attachments 为 null
        TraceScope traceScope = PowerMockito.mock(TraceScope.class);
        when(TraceThreadLocal.getScope()).thenReturn(traceScope);
        when(traceScope.getAttachments()).thenReturn(null);
        result = (Boolean) method.invoke(recommendService);
        Assert.assertFalse(result);

        // 测试 attachments 不为 null 但 REQUEST_ID 为空
        Map<String, String> attachments = new HashMap<>();
        attachments.put(TraceScope.ATTACHMENT_KEY.REQUEST_ID, "");
        when(traceScope.getAttachments()).thenReturn(attachments);
        result = (Boolean) method.invoke(recommendService);
        Assert.assertFalse(result);

        // 测试所有条件都满足
        attachments.put(TraceScope.ATTACHMENT_KEY.REQUEST_ID, "valid_trace_id");
        result = (Boolean) method.invoke(recommendService);
        Assert.assertTrue(result);
    }

    /**
     * 测试信息流刷新行为参数添加方法
     *
     * <p>验证 {@code addFeedsRefreshActionParams()} 方法能够根据偏移量正确设置刷新行为参数。
     * 该方法是重构后提取的私有方法，用于简化刷新行为判断逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>offset = 0（下拉刷新）- r_action = 0, r_page = downTimes</li>
     *   <li>offset > 0（翻页）- r_action = 1, r_page = upTimes</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将刷新行为判断逻辑独立为方法</li>
     *   <li>提高了代码的可读性和可维护性</li>
     *   <li>便于单独测试刷新行为逻辑</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testAddFeedsRefreshActionParams() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("addFeedsRefreshActionParams",
                Map.class, FeedsListReqProperty.class);
        method.setAccessible(true);

        // 测试下拉刷新（offset = 0）
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withOffset(0)
                .withDownTimes(2)
                .withUpTimes(0)
                .build();

        Map<String, Object> params = new HashMap<>();
        method.invoke(recommendService, params, testReqProperty);

        Assert.assertEquals(0, params.get(R_ACTION));
        Assert.assertEquals(2, params.get(R_PAGE));

        // 测试翻页（offset > 0）
        testReqProperty = new FeedsListReqPropertyBuilder()
                .withOffset(10)
                .withDownTimes(1)
                .withUpTimes(3)
                .build();

        params.clear();
        method.invoke(recommendService, params, testReqProperty);

        Assert.assertEquals(1, params.get(R_ACTION));
        Assert.assertEquals(3, params.get(R_PAGE));
    }

    /**
     * 测试信息流Cookie用户名有效性验证方法
     *
     * <p>验证 {@code isFeedsCookieUsernameValid()} 方法能够正确判断Cookie中用户名的有效性。
     * 该方法是重构后提取的私有方法，用于简化复杂的 && 条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>Cookie为null - 应返回false</li>
     *   <li>Cookie不为null但info为null - 应返回false</li>
     *   <li>info不为null但用户名为空 - 应返回false</li>
     *   <li>所有条件都满足 - 应返回true</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 && 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsFeedsCookieUsernameValid() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("isFeedsCookieUsernameValid", FeedsListReqProperty.class);
        method.setAccessible(true);

        // 测试Cookie为null - 直接创建空的FeedsListReqProperty
        FeedsListReqProperty testReqProperty = new FeedsListReqProperty();
        testReqProperty.setScookie(null);
        Boolean result = (Boolean) method.invoke(recommendService, testReqProperty);
        Assert.assertFalse(result);

        // 测试Cookie不为null但info为null
        Cookie cookie = PowerMockito.mock(Cookie.class);
        when(cookie.getInfo()).thenReturn(null);
        testReqProperty = new FeedsListReqPropertyBuilder().build();
        testReqProperty.setScookie(cookie);
        result = (Boolean) method.invoke(recommendService, testReqProperty);
        Assert.assertFalse(result);

        // 测试info不为null但用户名为空
        UserInfo cookieInfo = PowerMockito.mock(UserInfo.class);
        when(cookie.getInfo()).thenReturn(cookieInfo);
        when(cookieInfo.getUn()).thenReturn("");
        result = (Boolean) method.invoke(recommendService, testReqProperty);
        Assert.assertFalse(result);

        // 测试所有条件都满足
        when(cookieInfo.getUn()).thenReturn("test_user");
        result = (Boolean) method.invoke(recommendService, testReqProperty);
        Assert.assertTrue(result);
    }

    /**
     * 测试信息流用户名提取方法
     *
     * <p>验证 {@code extractFeedsUsername()} 方法能够正确从Cookie中提取用户名。
     * 该方法是重构后提取的私有方法，用于简化用户名获取逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>有效的Cookie用户名 - 应返回正确的用户名</li>
     *   <li>无效的Cookie - 应返回null</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将用户名提取逻辑独立为方法</li>
     *   <li>提高了代码的可读性和可维护性</li>
     *   <li>便于单独测试用户名提取逻辑</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testExtractFeedsUsername() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("extractFeedsUsername", FeedsListReqProperty.class);
        method.setAccessible(true);

        // 测试有效的Cookie用户名
        FeedsListReqProperty testReqProperty = new FeedsListReqPropertyBuilder()
                .withCookieUsername("test_username")
                .build();
        String result = (String) method.invoke(recommendService, testReqProperty);
        Assert.assertEquals("test_username", result);

        // 测试无效的Cookie - 直接创建空的FeedsListReqProperty
        testReqProperty = new FeedsListReqProperty();
        testReqProperty.setScookie(null);
        result = (String) method.invoke(recommendService, testReqProperty);
        Assert.assertNull(result);
    }

    /**
     * 测试信息流条件参数添加方法
     *
     * <p>验证 {@code addFeedsParamIfNotEmpty()} 方法能够正确处理参数的条件添加。
     * 该方法是重构后提取的通用工具方法，用于简化参数添加的条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>非空字符串 - 应该添加到参数Map中</li>
     *   <li>空字符串 - 不应该添加到参数Map中</li>
     *   <li>null值 - 不应该添加到参数Map中</li>
     * </ul>
     *
     * <h3>设计目的：</h3>
     * <ul>
     *   <li>避免在参数Map中添加无效的空值</li>
     *   <li>减少重复的条件判断代码</li>
     *   <li>提高代码的一致性和可维护性</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>消除了多处重复的 StringUtils.isNotEmpty() 判断</li>
     *   <li>提供了统一的参数添加逻辑</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testAddFeedsParamIfNotEmpty() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("addFeedsParamIfNotEmpty",
                Map.class, String.class, String.class);
        method.setAccessible(true);

        Map<String, Object> params = new HashMap<>();

        // 测试非空值
        method.invoke(recommendService, params, "key1", "value1");
        Assert.assertEquals("value1", params.get("key1"));

        // 测试空值
        method.invoke(recommendService, params, "key2", "");
        Assert.assertFalse(params.containsKey("key2"));

        // 测试null值
        method.invoke(recommendService, params, "key3", null);
        Assert.assertFalse(params.containsKey("key3"));
    }
}
