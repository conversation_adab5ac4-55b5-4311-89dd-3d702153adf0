package com.heytap.video.youli.builder.request;

import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.app.lib.cookie.UserInfo;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import org.mockito.Mockito;

import static org.mockito.Mockito.when;

/**
 * FeedsListReqProperty 测试数据构建器
 *
 * <p>使用 Builder 模式构建 {@link FeedsListReqProperty} 测试对象，
 * 简化测试数据的创建和维护，提供链式调用的流畅接口。</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/01
 * @see FeedsListReqProperty
 */
public class FeedsListReqPropertyBuilder {
    
    private final FeedsListReqProperty reqProperty;

    public FeedsListReqPropertyBuilder() {
        FeedsListReqProperty reqProperty = new FeedsListReqProperty();
        reqProperty.setMethod("");
        reqProperty.setF("pb");
        reqProperty.setKeyword("");
        reqProperty.setFeedssession("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoyMzAwOTA0NTUzMTkwNTYzODQsImF1ZCI6InZpZGVvIiwidmVyIjoyLCJyYXQiOjE1Njg3Mjc4NTEsInVubSI6Ik9QUE9fNDkxODc4ODYwIiwiaWQiOiI0OTE4Nzg4NjAiLCJleHAiOjE1ODYzMDE1NDYsImRjIjoidGVzdCJ9.DM6Gsv2JfrMCQ0eRhFEV8ntJzVIGFSpGn_ABYkX_SxI");
        reqProperty.setSource("youli");
        reqProperty.setVersion(31);
        reqProperty.setSession("eyJ0b3V0aWFvIjoiYV90PTMwMTU5NDkxMjc0NjE2NDQ4MzI5ODAzODk4OTZjNDE5OzQ5MTg3ODg2MCIsIm9wcG8iOiJvZnM9ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjFkSGx3SWpveExDSmlkV2xrSWpveU16QXdPVEEwTlRVek1Ua3dOVFl6T0RRc0ltRjFaQ0k2SW5acFpHVnZJaXdpZG1WeUlqb3lMQ0p5WVhRaU9qRTFOamczTWpjNE5URXNJblZ1YlNJNklrOVFVRTlmTkRreE9EYzRPRFl3SWl3aWFXUWlPaUkwT1RFNE56ZzROakFpTENKbGVIQWlPakUxT0RZek1ERTFORFlzSW1Saklqb2lkR1Z6ZENKOS5ETTZHc3YySmZyTUNRMGVSaEZFVjhudEp6VklHRlNwR25fQUJZa1hfU3hJIiwicyI6InRvdXRpYW8iLCJpbmZvIjp7InJ0IjoiMTU4NjI3Mjc0NiIsImZ0IjoiMTU2ODcyNzg1MSIsInVuIjoiT1BQT180OTE4Nzg4NjAiLCJ1aWQiOiI0OTE4Nzg4NjAiLCJzaWduIjoiMTg0OUQxRDVCMDg3N0YwNUNERjVFOEEzQzQ1QkEwQjkifX0=");
        reqProperty.setChannelSource("youli");
        reqProperty.setChannelName("--");
        reqProperty.setChannelShortName("--");
        reqProperty.setSearch(false);
        reqProperty.setRecVideo(true);
        reqProperty.setFromId("against_pnuemonia");
        reqProperty.setLimit(10);
        reqProperty.setPage(1);
        reqProperty.setOffset(0);
        reqProperty.setRefresh(1);
        reqProperty.setType("video");
        reqProperty.setRefreshTimes(1);
        reqProperty.setPreAdNumber(0);
        reqProperty.setEnterId(3);
        reqProperty.setIsHomePage(false);
        reqProperty.setOffline(false);
        reqProperty.setPromptRefreshCount(0);
        reqProperty.setSubRefreshTimes(1);
        reqProperty.setIsSubChannel(false);
        reqProperty.setInterestCardNewUser(false);
        reqProperty.setSaveMode(false);
        reqProperty.setIsDown(false);
        reqProperty.setUpTimes(1);
        reqProperty.setDownTimes(0);
        reqProperty.setEnableSportsLive(false);
        reqProperty.setEnableStock(false);
        reqProperty.setFirstLoginTime(1568727851);
        reqProperty.setSearchType(0);
        reqProperty.setDocid("");
        reqProperty.setStatisticsid("");
        reqProperty.setVideoName("");
        reqProperty.setRecType("");

        AttributeValues attributeValues = new AttributeValues();
        attributeValues.setRegion("CN");
        attributeValues.setIp("**************");
        attributeValues.setBrowser("3.1.0");
        attributeValues.setFullBrowserVersion("3.1.0");
        attributeValues.setClientFullBrowserVersion("3.1.0");
        attributeValues.setChannel("OPPO");
        attributeValues.setPhone("PADM00");
        attributeValues.setRom("Android9");
        attributeValues.setOsVersion("V6.0.1");
        attributeValues.setRomVersion("PADM00_11_C.14");
        attributeValues.setResolution("1080*2280");
        attributeValues.setLogic("320*676");
        attributeValues.setNetwork("wifi");
        attributeValues.setImei("867559045441455");
        attributeValues.setBrowserLanguage("zh-CN");
        attributeValues.setSystemLanguage("zh-CN");
        attributeValues.setUuid("6554db34e5f894adbfd3653716973ebd");
        attributeValues.setMp("China Mobie");
        attributeValues.setNewsSource("yidian");
        attributeValues.setVersionCode("20301000");
        attributeValues.setBrandChannel("20");
        attributeValues.setBuuid(230090455319056400L);
        attributeValues.setAid("88af3ae756a68f8d");
        attributeValues.setPkg("com.coloros.yoli");
        reqProperty.setAttributeValues(attributeValues);

        reqProperty.getAttachments().put("traceid", "cc0ecd2a7cbd464fb2f8ce1e2cd5caf1");
        reqProperty.getAttachments().put("content-length", "0");
        reqProperty.getAttachments().put("bc", "OPPO");
        reqProperty.getAttachments().put("duid", "");
        reqProperty.getAttachments().put("rv", "PCAM00_11_A.25");
        reqProperty.getAttachments().put("x-proto", "http");
        reqProperty.getAttachments().put("pcba", "001809709720033000009100");
        reqProperty.getAttachments().put("buuid", "230090455319056384");
        reqProperty.getAttachments().put("nt", "wifi");
        reqProperty.getAttachments().put("t-domain", "ivideo.test-browser.wanyol.com");
        reqProperty.getAttachments().put("cov", "V6.0.1");
        reqProperty.getAttachments().put("pkg", "com.coloros.yoli");
        reqProperty.getAttachments().put("uuid", "6554db34e5f894adbfd3653716973ebd");
        reqProperty.getAttachments().put("bvc", "20301000");
        reqProperty.getAttachments().put("br", "");
        reqProperty.getAttachments().put("dv", "PADM00");
        reqProperty.getAttachments().put("bv", "20.3.1.0der");
        reqProperty.getAttachments().put("request_receive_time", "1586256655138");
        reqProperty.getAttachments().put("host", "ivideo.test-browser.wanyol.com");
        reqProperty.getAttachments().put("sl", "zh-CN");
        reqProperty.getAttachments().put("content-type", "application/pb");
        reqProperty.getAttachments().put("connection", "keep-alive");
        reqProperty.getAttachments().put("cache-control", "no-cache");
        reqProperty.getAttachments().put("ss", "320*676");
        reqProperty.getAttachments().put("mp", "China Mobie");
        reqProperty.getAttachments().put("channelStrategy", "");
        reqProperty.getAttachments().put("level", "1.1.2");
        reqProperty.getAttachments().put("ov", "Android9");
        reqProperty.getAttachments().put("x-forwarded-for", "**************");
        reqProperty.getAttachments().put("response-format", "pb");
        reqProperty.getAttachments().put("mpo", "unknown Operator");
        reqProperty.getAttachments().put("accept", "*/*");
        reqProperty.getAttachments().put("x-real-ip", "**************");
        reqProperty.getAttachments().put("r", "CN");
        reqProperty.getAttachments().put("x-kkbrowser-ua", "605c91eeba04d097c5559cbb062f813dec0cffe75445592e2171441f0b43399842eb5c49a34fee6963c7ec2b756be0aa510342ec06054330abc20f234b9c77ccb6cc851f9e32f479493bfd8b1f9927359ed34c25bbfcecfb4f18e54b58632e017f82364ae4c1dba4c8931141f8d156eed29f0c0e77f9c5d1af4f310115eecb23377e7b6f381d6c591700d01fcad65cb47d1a46ae41db71aae14df813c63c81bde75e550670d3a7e0689c34e22a09f218f9b634bad77fc3ef55e4b5cd27e93fa0ff679a49a8ace9f246e1ce4d47745a75543e955f241de844e3670f5107246b6a4397f9e1f747c93844968378d261546030e4e74d61c2f760585773ba95a596591537edf070659324d5bf1161120613f660c72d0cbf7ada74cfa9a300ccf74eeea604bbcdcd912d1657d4357ddc2de6f830d6cdf10db6ff297c0ecbb553b7025bcf9b1a56bb647419afaf05e7bf08e911e7556667360bbf008b5c91d73e3c7a2df4effc0469145bfd6224696553459bc9cd709a653eaf4497e960d1d0681ada5426974d2755e661e4");
        reqProperty.getAttachments().put("ouid", "");
        reqProperty.getAttachments().put("av", "20.3.1.0der");
        reqProperty.getAttachments().put("x-user-ip", "**************");
        reqProperty.getAttachments().put("covc", "12");
        reqProperty.getAttachments().put("pi", "1080*2280");
        reqProperty.getAttachments().put("imei", "867559045441455");
        reqProperty.getAttachments().put("aid", "88af3ae756a68f8d");
        reqProperty.getAttachments().put("accept-encoding", "gzip,deflate");

        this.reqProperty = reqProperty;
    }

    /**
     * 设置剧集ID
     *
     * @param sid 剧集ID
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withSid(String sid) {
        reqProperty.setSid(sid);
        return this;
    }

    /**
     * 设置视频ID
     *
     * @param vid 视频ID
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withVid(String vid) {
        reqProperty.setVid(vid);
        return this;
    }

    /**
     * 设置内容池代码
     *
     * @param poolCode 内容池代码
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withPoolCode(String poolCode) {
        reqProperty.setPoolCode(poolCode);
        return this;
    }

    /**
     * 设置客户端版本
     *
     * @param version 客户端版本号（整数类型）
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withVersion(Integer version) {
        reqProperty.setVersion(version);
        return this;
    }

    /**
     * 设置获取更多视频开关
     *
     * @param fetchMoreVideosSwitch 获取更多视频开关
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withFetchMoreVideosSwitch(boolean fetchMoreVideosSwitch) {
        reqProperty.setFetchMoreVideosSwitch(fetchMoreVideosSwitch);
        return this;
    }

    /**
     * 设置内容数量
     *
     * @param contentCount 内容数量
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withContentCount(Integer contentCount) {
        reqProperty.setContentCount(contentCount);
        return this;
    }

    /**
     * 设置Cookie中的用户唯一标识
     *
     * @param cookieBuuid Cookie中的用户唯一标识
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withCookieBuuid(Long cookieBuuid) {
        Cookie cookie = Mockito.mock(Cookie.class);
        UserInfo cookieInfo = Mockito.mock(UserInfo.class);
        when(cookie.getInfo()).thenReturn(cookieInfo);
        when(cookieInfo.getBuuid()).thenReturn(cookieBuuid);
        reqProperty.setScookie(cookie);
        return this;
    }

    /**
     * 设置Cookie为null，用于测试无Cookie的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withNullCookie() {
        reqProperty.setScookie(null);
        // 清除可能包含用户信息的session数据
        reqProperty.setFeedssession(null);
        reqProperty.setSession(null);
        return this;
    }

    /**
     * 设置手机型号
     *
     * @param phone 手机型号，如 "OPPO_R15"、"PADM00" 等
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withPhone(String phone) {
        reqProperty.getAttributeValues().setPhone(phone);
        return this;
    }

    /**
     * 设置Cookie中的用户名
     *
     * @param username Cookie中的用户名
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withCookieUsername(String username) {
        Cookie cookie = Mockito.mock(Cookie.class);
        UserInfo cookieInfo = Mockito.mock(UserInfo.class);
        when(cookie.getInfo()).thenReturn(cookieInfo);
        when(cookieInfo.getUn()).thenReturn(username);
        reqProperty.setScookie(cookie);
        return this;
    }

    /**
     * 设置推荐类型
     *
     * @param recType 推荐类型，如 RecTypeConstant.H5 等
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withRecType(String recType) {
        reqProperty.setRecType(recType);
        return this;
    }

    /**
     * 设置请求类型
     *
     * @param type 请求类型，如 "video" 等
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withType(String type) {
        reqProperty.setType(type);
        return this;
    }

    /**
     * 设置地理位置信息
     *
     * @param area 地理位置JSON字符串
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withArea(String area) {
        reqProperty.setArea(area);
        return this;
    }

    /**
     * 设置根组ID
     *
     * @param rootGid 根组ID
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withRootGid(String rootGid) {
        reqProperty.setRootGid(rootGid);
        return this;
    }

    /**
     * 设置上级页面ID
     *
     * @param spageID 上级页面ID
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withSpageID(String spageID) {
        reqProperty.setSpageID(spageID);
        return this;
    }

    /**
     * 设置下拉次数
     *
     * @param downTimes 下拉次数
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withDownTimes(Integer downTimes) {
        reqProperty.setDownTimes(downTimes);
        return this;
    }

    /**
     * 设置上滑次数
     *
     * @param upTimes 上滑次数
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withUpTimes(Integer upTimes) {
        reqProperty.setUpTimes(upTimes);
        return this;
    }

    /**
     * 设置版本为null，用于测试无版本信息的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withNullVersion() {
        reqProperty.setVersion(null);
        return this;
    }

    /**
     * 设置请求ID
     *
     * @param requestId 请求ID
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withRequestId(String requestId) {
        reqProperty.setRequestId(requestId);
        return this;
    }

    /**
     * 设置来源ID
     *
     * @param fromId 来源ID
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withFromId(String fromId) {
        reqProperty.setFromId(fromId);
        return this;
    }

    /**
     * 设置偏移量
     *
     * @param offset 偏移量
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withOffset(Integer offset) {
        reqProperty.setOffset(offset);
        return this;
    }

    /**
     * 设置Push文档ID
     *
     * @param pushDocId Push文档ID
     * @return 当前构建器实例，支持链式调用
     */
    public FeedsListReqPropertyBuilder withPushDocId(String pushDocId) {
        reqProperty.setPushDocId(pushDocId);
        return this;
    }

    /**
     * 构建最终的 FeedsListReqProperty 对象
     *
     * @return 构建完成的 FeedsListReqProperty 实例
     */
    public FeedsListReqProperty build() {
        return this.reqProperty;
    }
}
