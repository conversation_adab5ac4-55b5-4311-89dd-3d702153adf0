package com.heytap.video.youli.spi.search;

import com.alibaba.fastjson.JSON;
import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.model.search.LongSearchHttpResponse;
import com.heytap.video.youli.model.search.LongSearchResult;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideo;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoActor;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoInterveneCard;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoRecommend;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoSeries;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideoTag;
import com.oppo.cpc.video.framework.lib.exception.InvalidDataRuntimeException;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * YouliVideoSearchLongSPI 单元测试类
 *
 * <p>主要测试重构后的 {@link YouliVideoSearchLongSPI#handleResult(SPIContext)} 方法
 * 及其拆分的子方法。该测试类验证了重构后代码的功能完整性、边界条件处理和异常情况处理。</p>
 *
 * <p>重构前的 handleResult 方法圈复杂度为 25，重构后主方法降低到 1，
 * 各个子方法都控制在 5 以下，通过方法分解、职责单一等技术手段提升了代码的可读性和可维护性。</p>
 *
 * <h3>测试覆盖范围：</h3>
 * <ul>
 *   <li>API配置获取：测试 getApiConfigName 方法</li>
 *   <li>参数构建：测试 getParams 方法在不同版本和场景下的参数构建</li>
 *   <li>设备类型获取：测试 getDeviceType 方法的各种边界情况</li>
 *   <li>响应处理：测试重构后的 handleResult 方法及其拆分的子方法</li>
 *   <li>异常处理：测试各种异常场景的处理逻辑</li>
 *   <li>边界条件：测试空值、空集合等边界情况</li>
 * </ul>
 *
 * <h3>重构验证重点：</h3>
 * <ul>
 *   <li>验证主方法handleResult的简化逻辑（圈复杂度从25降到1）</li>
 *   <li>验证validateResponse方法的响应验证逻辑</li>
 *   <li>验证buildBasicFeedsList方法的对象构建逻辑</li>
 *   <li>验证processAllVideoContent方法的统一处理逻辑</li>
 *   <li>验证各个process*方法的具体内容处理逻辑</li>
 *   <li>验证createBannerLongVideo方法的横幅对象创建</li>
 *   <li>验证searchItemToLongVideo方法的属性转换</li>
 *   <li>验证异常处理的正确性和完整性</li>
 * </ul>
 *
 * <h3>Mock 策略：</h3>
 * <ul>
 *   <li>使用 PowerMock 来 Mock 静态方法调用（JSON、StringUtils、CollectionUtils）</li>
 *   <li>使用 Mockito 来 Mock 复杂的依赖对象</li>
 *   <li>使用反射来测试私有方法</li>
 *   <li>Mock 第三方API响应和请求对象</li>
 * </ul>
 *
 * <h3>测试方法命名规范：</h3>
 * <ul>
 *   <li>testHandleResult_* : 测试重构后的主方法及其各种场景</li>
 *   <li>testGetApiConfigName : 测试API配置名称获取</li>
 *   <li>testGetDeviceType_* : 测试设备类型获取的各种情况</li>
 *   <li>testGetParams_* : 测试参数构建的各种场景</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/02
 * @see YouliVideoSearchLongSPI
 * @see SPIContext
 * @see FeedsList
 * @see LongSearchHttpResponse
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({JSON.class, StringUtils.class, CollectionUtils.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class YouliVideoSearchLongSPITest {

    /**
     * 被测试的SPI类实例，通过 @InjectMocks 自动注入 Mock 依赖
     *
     * <p>这是重构后的主要测试对象，包含了优化后的handleResult方法及其子方法</p>
     */
    @InjectMocks
    private YouliVideoSearchLongSPI youliVideoSearchLongSPI;

    /**
     * Mock 的优利API配置对象，用于提供测试所需的配置参数
     *
     * <p>主要用于测试getParams方法中的配置获取逻辑</p>
     */
    @Mock
    private YouliApiConfig youliApiConfig;

    /**
     * Mock 的SPI上下文对象，包含请求和响应信息
     *
     * <p>用于模拟第三方API调用的上下文环境，包含请求参数和响应数据</p>
     */
    @Mock
    private SPIContext<ListBaseReqProperty, Serializable> context;

    /**
     * Mock 的列表基础请求属性对象
     *
     * <p>包含搜索关键词、版本号、设备信息等请求参数</p>
     */
    @Mock
    private ListBaseReqProperty request;

    /**
     * Mock 的属性值对象，从 request 中获取
     *
     * <p>包含设备类型、用户标识等属性信息</p>
     */
    @Mock
    private AttributeValues attributeValues;

    /**
     * 测试用的长视频搜索HTTP响应对象
     *
     * <p>模拟第三方API返回的完整响应数据</p>
     */
    private LongSearchHttpResponse mockResponse;

    /**
     * 测试用的长视频搜索结果对象
     *
     * <p>包含具体的搜索结果数据，如视频列表、分页信息等</p>
     */
    private LongSearchResult mockResult;

    /**
     * 测试前的初始化方法
     *
     * <p>设置测试所需的 Mock 对象和测试数据，包括：</p>
     * <ul>
     *   <li>初始化测试用的响应对象和结果对象</li>
     *   <li>Mock 静态工具类方法（JSON、StringUtils、CollectionUtils）</li>
     *   <li>设置合理的默认返回值</li>
     * </ul>
     *
     * <p>该方法确保每个测试方法都有一个干净的测试环境</p>
     */
    @Before
    public void setUp() {
        setupTestData();
        setupStaticMocks();
    }

    /**
     * 设置测试数据
     *
     * <p>创建标准的测试响应对象和结果对象，包含：</p>
     * <ul>
     *   <li>LongSearchResult：页面大小10，有更多数据，搜索标签"test_tab"</li>
     *   <li>LongSearchHttpResponse：返回码0（成功），包含结果对象</li>
     * </ul>
     *
     * <p>这些默认数据代表一个成功的API响应，可以在具体测试中进行修改</p>
     */
    private void setupTestData() {
        mockResult = new LongSearchResult();
        mockResult.setPageSize(10);
        mockResult.setHasMore(1);
        mockResult.setSearchTab("test_tab");

        mockResponse = new LongSearchHttpResponse();
        mockResponse.setRet(0);
        mockResponse.setResult(mockResult);
    }

    /**
     * 设置静态方法的Mock
     *
     * <p>Mock 常用的静态工具类方法，确保测试的独立性和可控性：</p>
     * <ul>
     *   <li>JSON.class：用于JSON序列化和反序列化</li>
     *   <li>StringUtils.class：用于字符串工具方法</li>
     *   <li>CollectionUtils.class：用于集合工具方法</li>
     * </ul>
     *
     * <p>这些Mock确保测试不依赖于外部工具类的具体实现</p>
     */
    private void setupStaticMocks() {
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(StringUtils.class);
        PowerMockito.mockStatic(CollectionUtils.class);
    }

    /**
     * 测试重构后的handleResult主方法 - 成功场景
     *
     * <p>验证 {@link YouliVideoSearchLongSPI#handleResult(SPIContext)} 方法
     * 在正常情况下能够成功处理第三方API响应，并正确转换为系统内部格式。
     * 这是重构后主方法的核心功能测试。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>有效的JSON响应数据</li>
     *   <li>返回码为0（成功）</li>
     *   <li>包含完整的结果对象</li>
     *   <li>包含正确的分页信息</li>
     * </ul>
     *
     * <h3>验证重构效果：</h3>
     * <ul>
     *   <li>主方法逻辑简洁清晰（圈复杂度从25降到1）</li>
     *   <li>validateResponse方法正确验证响应</li>
     *   <li>buildBasicFeedsList方法正确构建基础对象</li>
     *   <li>processAllVideoContent方法统一处理内容</li>
     *   <li>各个子方法协调工作</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回成功的VideoBaseRt对象</li>
     *   <li>FeedsList包含正确的分页信息（offset=10, hasMore=true）</li>
     *   <li>包含正确的搜索标签（"test_tab"）</li>
     * </ul>
     */
    @Test
    public void testHandleResult_Success() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        Assert.assertEquals("偏移量应该正确", Integer.valueOf(10), result.getData().getOffset());
        Assert.assertTrue("应该有更多数据", result.getData().getHasMore());
        Assert.assertEquals("搜索标签应该匹配", "test_tab", result.getData().getSearchTab());
    }

    /**
     * 测试validateResponse方法 - 空响应异常处理
     *
     * <p>验证 {@link YouliVideoSearchLongSPI#handleResult(SPIContext)} 方法
     * 在响应为null时的异常处理逻辑。重构后的validateResponse方法应该能够
     * 正确识别并处理这种异常情况。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>context.getResponse()返回null</li>
     *   <li>模拟网络异常或API无响应的情况</li>
     *   <li>StringUtils.isEmpty(null)返回true</li>
     * </ul>
     *
     * <h3>验证重构效果：</h3>
     * <ul>
     *   <li>validateResponse方法正确处理null响应</li>
     *   <li>抛出合适的InvalidDataRuntimeException异常</li>
     *   <li>异常信息包含"getLongSearchResult error"</li>
     *   <li>异常处理逻辑从主方法中独立出来</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>抛出InvalidDataRuntimeException异常</li>
     *   <li>异常消息包含错误描述信息</li>
     *   <li>不会继续执行后续的处理逻辑</li>
     * </ul>
     */
    @Test
    public void testHandleResult_NullResponse() {
        // 准备测试数据
        PowerMockito.when(StringUtils.isEmpty(null)).thenReturn(true);
        when(context.getResponse()).thenReturn(null);
        when(context.getRequest()).thenReturn(request);

        // 执行测试并验证异常
        try {
            youliVideoSearchLongSPI.handleResult(context);
            Assert.fail("应该抛出InvalidDataRuntimeException异常");
        } catch (InvalidDataRuntimeException e) {
            Assert.assertNotNull("异常不应为空", e);
            Assert.assertTrue("异常消息应该包含错误信息", e.getMessage().contains("getLongSearchResult error"));
        }
    }

    /**
     * 测试validateResponse方法 - 无效返回码异常处理
     *
     * <p>验证 {@link YouliVideoSearchLongSPI#handleResult(SPIContext)} 方法
     * 在第三方API返回非0返回码时的异常处理逻辑。重构后的validateResponse方法
     * 应该能够正确识别并处理API调用失败的情况。</p>
     *
     * <h3>测试场景详述：</h3>
     * <ul>
     *   <li><strong>API状态</strong>：第三方API返回码为1（表示失败）</li>
     *   <li><strong>响应格式</strong>：JSON格式正常，但业务逻辑失败</li>
     *   <li><strong>解析状态</strong>：JSON.parseObject()能正常解析响应</li>
     *   <li><strong>业务规则</strong>：只有返回码为0才表示成功</li>
     * </ul>
     *
     * <h3>验证重构效果：</h3>
     * <ul>
     *   <li><strong>职责分离</strong>：validateResponse方法独立处理响应验证</li>
     *   <li><strong>异常处理</strong>：正确抛出InvalidDataRuntimeException</li>
     *   <li><strong>错误信息</strong>：异常信息包含具体的返回码</li>
     *   <li><strong>流程控制</strong>：异常处理逻辑从主方法中独立出来</li>
     * </ul>
     *
     * <h3>Mock策略：</h3>
     * <ul>
     *   <li>设置mockResponse.setRet(1)模拟失败返回码</li>
     *   <li>Mock StringUtils.isEmpty()返回false（响应非空）</li>
     *   <li>Mock JSON.parseObject()返回设置好的mockResponse</li>
     *   <li>Mock context返回测试用的响应和请求</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>抛出InvalidDataRuntimeException异常</li>
     *   <li>异常对象不为null</li>
     *   <li>不会继续执行后续的处理逻辑</li>
     * </ul>
     */
    @Test
    public void testHandleResult_InvalidRetCode() {
        // 准备测试数据 - 设置失败的返回码
        mockResponse.setRet(1);
        String responseJson = "{\"ret\":1,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        PowerMockito.when(StringUtils.isEmpty(responseJson)).thenReturn(false);
        PowerMockito.when(JSON.parseObject(eq(responseJson), eq(LongSearchHttpResponse.class))).thenReturn(mockResponse);

        when(context.getResponse()).thenReturn(responseJson);
        when(context.getRequest()).thenReturn(request);

        // 执行测试并验证异常
        try {
            youliVideoSearchLongSPI.handleResult(context);
            Assert.fail("应该抛出InvalidDataRuntimeException异常");
        } catch (InvalidDataRuntimeException e) {
            Assert.assertNotNull("异常不应为空", e);
        }
    }

    /**
     * 测试processLongVideoSearchResult方法的功能
     *
     * <p>验证重构后的 {@link YouliVideoSearchLongSPI#processLongVideoSearchResult} 方法
     * 能够正确处理长视频搜索结果。该方法是从原handleResult方法中提取出来的独立处理逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>响应包含LongVideoSearchResult列表</li>
     *   <li>搜索结果包含有效的长视频对象</li>
     *   <li>CollectionUtils.isNotEmpty返回true</li>
     * </ul>
     *
     * <h3>验证重构效果：</h3>
     * <ul>
     *   <li>processLongVideoSearchResult方法独立处理搜索结果</li>
     *   <li>正确调用searchItemToLongVideo转换视频属性</li>
     *   <li>正确创建Item对象并设置属性</li>
     *   <li>正确添加到FeedsList的longVideoList和itemList中</li>
     *   <li>方法职责单一，圈复杂度低</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回成功的结果</li>
     *   <li>FeedsList包含长视频列表</li>
     *   <li>长视频对象属性被正确转换</li>
     * </ul>
     */
    @Test
    public void testHandleResult_WithSearchResult() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        List<LongVideo> longVideoList = createMockLongVideoList();
        mockResult.setLongVideoSearchResult(longVideoList);
        PowerMockito.when(CollectionUtils.isEmpty(longVideoList)).thenReturn(false);
        PowerMockito.when(CollectionUtils.isNotEmpty(longVideoList)).thenReturn(true);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        Assert.assertNotNull("长视频列表不应为空", result.getData().getLongVideos());
    }

    /**
     * 测试processLongVideoRecommend方法的功能
     *
     * <p>验证重构后的 {@link YouliVideoSearchLongSPI#processLongVideoRecommend} 方法
     * 能够正确处理长视频推荐内容。该方法是从原handleResult方法中提取出来的独立处理逻辑，
     * 专门负责处理第三方API返回的推荐视频内容。</p>
     *
     * <h3>测试场景详述：</h3>
     * <ul>
     *   <li><strong>数据结构</strong>：响应包含LongVideoRecommend对象</li>
     *   <li><strong>内容验证</strong>：推荐内容包含有效的长视频列表</li>
     *   <li><strong>集合状态</strong>：CollectionUtils.isNotEmpty返回true</li>
     *   <li><strong>业务流程</strong>：推荐内容需要经过searchItemToLongVideo转换</li>
     * </ul>
     *
     * <h3>验证重构效果：</h3>
     * <ul>
     *   <li><strong>方法独立性</strong>：processLongVideoRecommend方法独立处理推荐内容</li>
     *   <li><strong>数据转换</strong>：正确调用searchItemToLongVideo转换视频属性</li>
     *   <li><strong>结果设置</strong>：正确设置到FeedsList的longVideoRecommend中</li>
     *   <li><strong>职责单一</strong>：方法职责单一，圈复杂度低</li>
     *   <li><strong>可测试性</strong>：独立的方法便于单独测试和验证</li>
     * </ul>
     *
     * <h3>Mock策略：</h3>
     * <ul>
     *   <li>Mock JSON解析返回正确的响应对象</li>
     *   <li>创建LongVideoRecommend对象并设置内容列表</li>
     *   <li>Mock CollectionUtils的isEmpty和isNotEmpty方法</li>
     *   <li>设置mockResult包含推荐内容</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回成功的VideoBaseRt对象</li>
     *   <li>FeedsList包含非空的推荐内容</li>
     *   <li>推荐内容中的长视频属性被正确转换</li>
     * </ul>
     */
    @Test
    public void testHandleResult_WithRecommendContent() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        LongVideoRecommend recommend = new LongVideoRecommend();
        List<LongVideo> recommendList = createMockLongVideoList();
        recommend.setContents(recommendList);
        mockResult.setLongVideoRecommend(recommend);
        PowerMockito.when(CollectionUtils.isEmpty(recommendList)).thenReturn(false);
        PowerMockito.when(CollectionUtils.isNotEmpty(recommendList)).thenReturn(true);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        Assert.assertNotNull("推荐内容不应为空", result.getData().getLongVideoRecommend());
    }

    /**
     * 测试processLongVideoBannerList方法的功能
     *
     * <p>验证重构后的 {@link YouliVideoSearchLongSPI#processLongVideoBannerList} 方法
     * 能够正确处理长视频横幅列表。该方法包含特殊的横幅对象创建逻辑，
     * 是重构后独立出来的复杂处理逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>响应包含LongVideoBannerList</li>
     *   <li>横幅列表包含LvDrawerItemVO对象</li>
     *   <li>每个横幅项包含图片URL和深度链接</li>
     * </ul>
     *
     * <h3>验证重构效果：</h3>
     * <ul>
     *   <li>processLongVideoBannerList方法独立处理横幅逻辑</li>
     *   <li>createBannerLongVideo方法正确创建横幅视频对象</li>
     *   <li>正确设置contentType为"banner"</li>
     *   <li>正确设置horizontalIcon和deepLink属性</li>
     *   <li>复杂的横幅处理逻辑被模块化</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回成功的结果</li>
     *   <li>FeedsList包含横幅列表</li>
     *   <li>横幅对象的contentType为"banner"</li>
     *   <li>横幅对象包含正确的图片和链接信息</li>
     * </ul>
     */
    @Test
    public void testHandleResult_WithBannerList() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        List<LvDrawerItemVO> bannerList = createMockBannerList();
        mockResult.setLongVideoBannerList(bannerList);
        PowerMockito.when(CollectionUtils.isEmpty(bannerList)).thenReturn(false);
        PowerMockito.when(CollectionUtils.isNotEmpty(bannerList)).thenReturn(true);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        Assert.assertNotNull("横幅列表不应为空", result.getData().getLongVideoBannerList());
        Assert.assertEquals("横幅内容类型应该正确", "banner",
            result.getData().getLongVideoBannerList().get(0).getContentType());
    }

    /**
     * 测试API配置名称获取方法
     *
     * <p>验证 {@link YouliVideoSearchLongSPI#getApiConfigName()} 方法能够正确返回
     * 长视频搜索接口的配置名称。该方法是SPI接口的基础方法，用于标识具体的API配置，
     * 框架会根据此名称来查找对应的API配置信息。</p>
     *
     * <h3>测试目的：</h3>
     * <ul>
     *   <li><strong>配置标识</strong>：验证返回正确的API配置标识符</li>
     *   <li><strong>框架集成</strong>：确保与SPI框架的正确集成</li>
     *   <li><strong>配置映射</strong>：验证配置名称与枚举值的一致性</li>
     * </ul>
     *
     * <h3>业务意义：</h3>
     * <ul>
     *   <li>该配置名称用于框架识别和路由API请求</li>
     *   <li>配置名称与YAML配置文件中的API配置项对应</li>
     *   <li>错误的配置名称会导致API调用失败</li>
     * </ul>
     *
     * <h3>验证内容：</h3>
     * <ul>
     *   <li>返回值与 {@link ApiConfigNameEnum#SEARCHLONG} 的名称一致</li>
     *   <li>方法调用的稳定性和一致性</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回 {@link ApiConfigNameEnum#SEARCHLONG#getName()} 的值</li>
     *   <li>返回值为非空字符串</li>
     * </ul>
     */
    @Test
    public void testGetApiConfigName() {
        // 执行测试
        String result = youliVideoSearchLongSPI.getApiConfigName();

        // 验证结果
        Assert.assertEquals("API配置名称应该匹配", ApiConfigNameEnum.SEARCHLONG.getName(), result);
        Assert.assertNotNull("配置名称不应为空", result);
        Assert.assertFalse("配置名称不应为空字符串", result.trim().isEmpty());
    }

    /**
     * 测试设备类型获取方法 - 正常情况
     *
     * <p>验证 {@link YouliVideoSearchLongSPI#getDeviceType(ListBaseReqProperty)} 方法
     * 在正常情况下能够正确获取设备类型。该方法从请求属性中提取设备类型信息，
     * 并转换为字符串格式。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>设备类型为有效的整数值（1）</li>
     *   <li>AttributeValues对象正常可用</li>
     *   <li>无异常情况</li>
     * </ul>
     *
     * <h3>验证逻辑：</h3>
     * <ul>
     *   <li>正确调用request.getAttributeValues()</li>
     *   <li>正确调用attributeValues.getDeviceType()</li>
     *   <li>正确转换整数为字符串</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回设备类型的字符串表示（"1"）</li>
     * </ul>
     */
    @Test
    public void testGetDeviceType_Normal() {
        // 准备测试数据
        when(request.getAttributeValues()).thenReturn(attributeValues);
        when(attributeValues.getDeviceType()).thenReturn(1);

        // 执行测试
        String result = youliVideoSearchLongSPI.getDeviceType(request);

        // 验证结果
        Assert.assertEquals("设备类型应该匹配", "1", result);
    }

    /**
     * 测试设备类型获取方法 - null值处理
     *
     * <p>验证 {@link YouliVideoSearchLongSPI#getDeviceType(ListBaseReqProperty)} 方法
     * 在设备类型为null时的处理逻辑。根据业务规则，null值应该被转换为默认值"0"。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>设备类型为null</li>
     *   <li>AttributeValues对象正常可用</li>
     *   <li>需要返回默认值</li>
     * </ul>
     *
     * <h3>验证业务规则：</h3>
     * <ul>
     *   <li>null值被正确识别</li>
     *   <li>返回默认设备类型"0"</li>
     *   <li>不会抛出NullPointerException</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回默认设备类型"0"</li>
     * </ul>
     */
    @Test
    public void testGetDeviceType_Null() {
        // 准备测试数据
        when(request.getAttributeValues()).thenReturn(attributeValues);
        when(attributeValues.getDeviceType()).thenReturn(null);

        // 执行测试
        String result = youliVideoSearchLongSPI.getDeviceType(request);

        // 验证结果
        Assert.assertEquals("null设备类型应该返回默认值", "0", result);
    }

    /**
     * 测试processLongVideoSeries方法
     */
    @Test
    public void testHandleResult_WithSeriesContent() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        LongVideoSeries series = new LongVideoSeries();
        List<LongVideo> seriesList = createMockLongVideoList();
        series.setContents(seriesList);
        mockResult.setLongVideoSeries(series);
        PowerMockito.when(CollectionUtils.isEmpty(seriesList)).thenReturn(false);
        PowerMockito.when(CollectionUtils.isNotEmpty(seriesList)).thenReturn(true);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        Assert.assertNotNull("系列内容不应为空", result.getData().getLongVideoSeries());
    }

    /**
     * 测试processLongVideoTag方法
     */
    @Test
    public void testHandleResult_WithTagContent() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        LongVideoTag tag = new LongVideoTag();
        List<LongVideo> tagList = createMockLongVideoList();
        tag.setContents(tagList);
        mockResult.setLongVideoTag(tag);
        PowerMockito.when(CollectionUtils.isEmpty(tagList)).thenReturn(false);
        PowerMockito.when(CollectionUtils.isNotEmpty(tagList)).thenReturn(true);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        Assert.assertNotNull("标签内容不应为空", result.getData().getLongVideoTag());
    }

    /**
     * 测试processLongVideoDefaultRecommend方法
     */
    @Test
    public void testHandleResult_WithDefaultRecommend() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        LongVideoInterveneCard defaultRecommend = new LongVideoInterveneCard();
        List<LongVideo> defaultList = createMockLongVideoList();
        defaultRecommend.setContents(defaultList);
        mockResult.setLongVideoDefaultRecommend(defaultRecommend);
        PowerMockito.when(CollectionUtils.isEmpty(defaultList)).thenReturn(false);
        PowerMockito.when(CollectionUtils.isNotEmpty(defaultList)).thenReturn(true);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        Assert.assertNotNull("默认推荐不应为空", result.getData().getLongVideoDefaultRecommend());
    }

    /**
     * 测试processLongVideoActor方法的功能
     *
     * <p>验证重构后的 {@link YouliVideoSearchLongSPI#processLongVideoActor} 方法
     * 能够正确处理长视频演员内容。该方法是重构后独立出来的演员内容处理逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>响应包含LongVideoActor对象</li>
     *   <li>演员内容包含有效的长视频列表</li>
     *   <li>CollectionUtils.isNotEmpty返回true</li>
     * </ul>
     *
     * <h3>验证重构效果：</h3>
     * <ul>
     *   <li>processLongVideoActor方法独立处理演员内容</li>
     *   <li>正确调用searchItemToLongVideo转换视频属性</li>
     *   <li>正确设置到FeedsList的longVideoActor中</li>
     *   <li>方法职责单一，逻辑清晰</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回成功的结果</li>
     *   <li>FeedsList包含演员内容</li>
     *   <li>演员相关的长视频属性被正确转换</li>
     * </ul>
     */
    @Test
    public void testHandleResult_WithActorContent() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        LongVideoActor actor = new LongVideoActor();
        List<LongVideo> actorList = createMockLongVideoList();
        actor.setContents(actorList);
        mockResult.setLongVideoActor(actor);
        PowerMockito.when(CollectionUtils.isEmpty(actorList)).thenReturn(false);
        PowerMockito.when(CollectionUtils.isNotEmpty(actorList)).thenReturn(true);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        Assert.assertNotNull("演员内容不应为空", result.getData().getLongVideoActor());
    }

    /**
     * 测试空集合的处理 - 验证重构后的空值处理逻辑
     */
    @Test
    public void testHandleResult_EmptyCollections() {
        // 准备测试数据
        String responseJson = "{\"ret\":0,\"result\":{\"pageSize\":10,\"hasMore\":1,\"searchTab\":\"test_tab\"}}";
        setupSuccessResponseMocks(responseJson);

        // 设置空集合
        mockResult.setLongVideoSearchResult(new ArrayList<>());
        PowerMockito.when(CollectionUtils.isEmpty(any())).thenReturn(true);
        PowerMockito.when(CollectionUtils.isNotEmpty(any())).thenReturn(false);

        // 执行测试
        VideoBaseRt<FeedsList> result = youliVideoSearchLongSPI.handleResult(context);

        // 验证结果 - 空集合不应该被设置
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该返回成功结果", VideoBaseRt.isSuccess(result));
        Assert.assertNotNull("数据不应为空", result.getData());
        // 验证空集合处理正确
        Assert.assertNull("空推荐内容不应被设置", result.getData().getLongVideoRecommend());
    }

    /**
     * 测试版本小于52300时的参数构建逻辑
     *
     * <p>验证 {@link YouliVideoSearchLongSPI#getParams(SPIContext)} 方法在客户端版本
     * 小于52300时的特殊参数处理逻辑。在低版本客户端中，系统需要从请求的attachments
     * 中获取longNum参数，并据此设置limit和page值。</p>
     *
     * <h3>测试场景详述：</h3>
     * <ul>
     *   <li><strong>版本条件</strong>：客户端版本52200（小于阈值52300）</li>
     *   <li><strong>参数来源</strong>：从request.getAttachments()中获取"longNum"参数</li>
     *   <li><strong>业务逻辑</strong>：longNum值用于设置请求的limit和page属性</li>
     *   <li><strong>兼容性</strong>：确保低版本客户端的向后兼容性</li>
     * </ul>
     *
     * <h3>验证的业务规则：</h3>
     * <ul>
     *   <li>当版本 < 52300时，从attachments获取longNum参数</li>
     *   <li>调用request.setLimit(longNum的整数值)设置限制数量</li>
     *   <li>调用request.setPage(1)重置页码为第一页</li>
     *   <li>返回的参数映射包含所有必要的请求参数</li>
     *   <li>关键词等基础参数正确传递</li>
     * </ul>
     *
     * <h3>Mock策略：</h3>
     * <ul>
     *   <li>Mock context.getRequest()返回测试用的request对象</li>
     *   <li>Mock request.getVersion()返回52200（低版本）</li>
     *   <li>Mock request.getAttachments()返回包含longNum的Map</li>
     *   <li>使用setupBasicRequestMocks()设置其他必要参数</li>
     * </ul>
     *
     * <h3>预期结果：</h3>
     * <ul>
     *   <li>返回非空的参数映射Map</li>
     *   <li>验证request.setLimit(5)被正确调用</li>
     *   <li>验证request.setPage(1)被正确调用</li>
     *   <li>验证返回的参数包含正确的keyword值</li>
     * </ul>
     */
    @Test
    public void testGetParams_VersionLessThan52300() {
        // 准备测试数据
        when(context.getRequest()).thenReturn(request);
        when(request.getVersion()).thenReturn(52200);

        Map<String, String> attachments = new HashMap<>();
        attachments.put("longNum", "5");
        when(request.getAttachments()).thenReturn(attachments);
        setupBasicRequestMocks();

        // 执行测试
        Map<String, String> result = youliVideoSearchLongSPI.getParams(context);

        // 验证结果
        Assert.assertNotNull("参数映射不应为空", result);
        Assert.assertFalse("参数映射不应为空集合", result.isEmpty());

        // 验证方法调用
        verify(request).setLimit(5);
        verify(request).setPage(1);

        // 验证参数内容
        Assert.assertEquals("关键词参数应该匹配", "test_keyword", result.get("keyword"));
        Assert.assertEquals("搜索类型参数应该匹配", "0", result.get("searchType"));
        Assert.assertEquals("页面大小参数应该匹配", "5", result.get("pageSize"));
        Assert.assertEquals("页码参数应该匹配", "1", result.get("pageIndex"));

        // 验证必要参数存在
        Assert.assertTrue("应该包含关键词参数", result.containsKey("keyword"));
        Assert.assertTrue("应该包含搜索类型参数", result.containsKey("searchType"));
        Assert.assertTrue("应该包含页面大小参数", result.containsKey("pageSize"));
        Assert.assertTrue("应该包含页码参数", result.containsKey("pageIndex"));
    }

    /**
     * 设置成功响应的通用Mock
     *
     * <p>为handleResult相关测试提供通用的成功响应Mock设置，减少重复代码。</p>
     *
     * @param responseJson JSON响应字符串
     */
    private void setupSuccessResponseMocks(String responseJson) {
        // Mock 静态方法调用
        PowerMockito.when(StringUtils.isEmpty(responseJson)).thenReturn(false);
        PowerMockito.when(StringUtils.isEmpty(any(String.class))).thenReturn(false);
        PowerMockito.when(JSON.parseObject(eq(responseJson), eq(LongSearchHttpResponse.class))).thenReturn(mockResponse);

        // Mock 上下文和请求
        when(context.getResponse()).thenReturn(responseJson);
        when(context.getRequest()).thenReturn(request);
        when(request.getOffset()).thenReturn(0);
    }

    /**
     * 设置基础请求模拟对象
     *
     * <p>为测试方法提供标准的请求对象Mock设置，包括所有必要的参数。
     * 该方法设置了getParams方法测试所需的所有请求参数。</p>
     *
     * <h3>设置的参数包括：</h3>
     * <ul>
     *   <li>搜索相关：keyword="test_keyword", searchType=0</li>
     *   <li>分页相关：page=1, limit=10</li>
     *   <li>用户相关：vipType="test_vip", version=52300</li>
     *   <li>会话相关：appId, requestId, session, feedssession</li>
     *   <li>设备相关：quickEngineVersion=1, lastSearchTab="test_tab"</li>
     *   <li>属性相关：phone, buuid, deviceType</li>
     * </ul>
     *
     * <p>这些参数覆盖了getParams方法中使用的所有请求属性</p>
     */
    private void setupBasicRequestMocks() {
        when(request.getKeyword()).thenReturn("test_keyword");
        when(request.getSearchType()).thenReturn(0);
        when(request.getPage()).thenReturn(1);
        when(request.getLimit()).thenReturn(10);
        when(request.getVipType()).thenReturn("test_vip");
        when(request.getVersion()).thenReturn(52300);
        when(request.getAppId()).thenReturn("test_app");
        when(request.getRequestId()).thenReturn("test_request");
        when(request.getSession()).thenReturn("test_session");
        when(request.getFeedssession()).thenReturn("test_feeds_session");
        when(request.getQuickEngineVersion()).thenReturn(1);
        when(request.getLastSearchTab()).thenReturn("test_tab");

        when(request.getAttributeValues()).thenReturn(attributeValues);
        when(attributeValues.getPhone()).thenReturn("test_phone");
        when(attributeValues.getBuuid()).thenReturn(123456L);
        when(attributeValues.getDeviceType()).thenReturn(1);
    }

    /**
     * 创建模拟的长视频列表
     *
     * <p>为测试提供标准的长视频对象列表，用于验证各种内容处理方法。
     * 创建的长视频对象包含测试searchItemToLongVideo方法所需的属性。</p>
     *
     * <h3>创建的长视频对象属性：</h3>
     * <ul>
     *   <li>sid: "test_sid_1" - 视频唯一标识</li>
     *   <li>multipleSourceCode: ["source1", "source2"] - 多源代码列表</li>
     * </ul>
     *
     * <p>该列表用于测试以下重构后的方法：</p>
     * <ul>
     *   <li>processLongVideoSearchResult</li>
     *   <li>processLongVideoRecommend</li>
     *   <li>processLongVideoSeries</li>
     *   <li>processLongVideoTag</li>
     *   <li>processLongVideoDefaultRecommend</li>
     *   <li>processLongVideoActor</li>
     * </ul>
     *
     * @return 包含测试数据的长视频列表
     */
    private List<LongVideo> createMockLongVideoList() {
        List<LongVideo> longVideoList = new ArrayList<>();
        LongVideo longVideo = new LongVideo();
        longVideo.setSid("test_sid_1");
        longVideo.setMultipleSourceCode(Arrays.asList("source1", "source2"));
        longVideoList.add(longVideo);
        return longVideoList;
    }

    /**
     * 创建模拟的横幅列表
     *
     * <p>为测试提供标准的横幅项目列表，用于验证processLongVideoBannerList方法
     * 和createBannerLongVideo方法的功能。</p>
     *
     * <h3>创建的横幅对象属性：</h3>
     * <ul>
     *   <li>imgUrl: "<a href="http://test.com/image.jpg">横幅图片</a>" - 横幅图片URL</li>
     *   <li>deepLink: "<a href="http://test.com/deeplink">横幅深度链接</a>" - 横幅深度链接</li>
     * </ul>
     *
     * <p>该列表用于测试重构后的横幅处理逻辑，验证：</p>
     * <ul>
     *   <li>横幅对象的正确创建</li>
     *   <li>contentType设置为"banner"</li>
     *   <li>horizontalIcon和deepLink的正确设置</li>
     * </ul>
     *
     * @return 包含测试数据的横幅项目列表
     */
    private List<LvDrawerItemVO> createMockBannerList() {
        List<LvDrawerItemVO> bannerList = new ArrayList<>();
        LvDrawerItemVO banner = new LvDrawerItemVO();
        banner.setImgUrl("http://test.com/image.jpg");
        banner.setDeepLink("http://test.com/deeplink");
        bannerList.add(banner);
        return bannerList;
    }
}
